<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期标题显示测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .date-display {
            font-size: 16px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 5px;
            margin: 10px 0;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>日期标题显示测试</h1>
        <p>测试日期匹配逻辑和标题显示是否严格按照README.md中的优先级顺序</p>
        
        <div class="test-section">
            <div class="test-title">1. 上传测试文件</div>
            <div>
                <label>销售价目表：</label>
                <input type="file" id="salesFile" accept=".xlsx,.xls">
            </div>
            <div style="margin-top: 10px;">
                <label>菜篮子价格表：</label>
                <input type="file" id="basketFile" accept=".xlsx,.xls">
            </div>
            <button class="test-button" onclick="loadTestFiles()">开始分析</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 日期匹配结果</div>
            <div class="date-display" id="dateResult">等待文件上传...</div>
            <div>
                <strong>预期优先级顺序：</strong>25日 → 26日 → 27日 → 28日 → 29日
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 标题显示测试</div>
            <div class="date-display" id="titleDisplay">等待分析完成...</div>
            <button class="test-button" onclick="testTitleDisplay()">测试标题显示</button>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. 详细日志</div>
            <div class="log-output" id="logOutput">等待开始测试...</div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        let priceComparator = null;
        
        // 重写console.log来捕获日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const logOutput = document.getElementById('logOutput');
            if (logOutput) {
                logOutput.textContent += args.join(' ') + '\n';
                logOutput.scrollTop = logOutput.scrollHeight;
            }
        };
        
        async function loadTestFiles() {
            const salesFile = document.getElementById('salesFile').files[0];
            const basketFile = document.getElementById('basketFile').files[0];
            
            if (!salesFile || !basketFile) {
                alert('请选择两个文件');
                return;
            }
            
            try {
                // 清空日志
                document.getElementById('logOutput').textContent = '';
                console.log('=== 开始日期匹配测试 ===');
                
                // 创建价格比较器实例
                priceComparator = new PriceComparator();
                
                // 加载文件
                console.log('正在加载销售价目表...');
                await priceComparator.loadSalesFile(salesFile);
                
                console.log('正在加载菜篮子价格表...');
                await priceComparator.loadBasketFile(basketFile);
                
                // 分析日期匹配
                console.log('正在分析日期匹配...');
                const basketStructure = priceComparator.analyzeBasketStructure();
                const bestDateResult = priceComparator.findBestDateColumn(basketStructure);

                // 模拟完整的比价流程，确保bestDateInfo被正确设置
                console.log('模拟完整比价流程...');
                priceComparator.bestDateInfo = bestDateResult; // 手动设置，模拟实际流程

                // 显示结果
                const dateInfo = bestDateResult.dateInfo;
                const resultText = `
选中日期：${dateInfo.day}日
完整日期：${dateInfo.year || 2025}年${(dateInfo.month || 6).toString().padStart(2, '0')}月${dateInfo.day.toString().padStart(2, '0')}日
优先级：${dateInfo.priority}
是否本期：${dateInfo.isCurrentPeriod ? '是' : '否'}
列名：${dateInfo.column}
显示名称：${dateInfo.displayName || '无'}
                `;

                document.getElementById('dateResult').textContent = resultText;

                // 测试标题显示
                testTitleDisplay();
                
            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('dateResult').textContent = '测试失败: ' + error.message;
            }
        }
        
        function testTitleDisplay() {
            if (!priceComparator) {
                document.getElementById('titleDisplay').textContent = '请先加载文件';
                return;
            }

            console.log('=== 测试标题显示 ===');

            // 检查bestDateInfo是否存在
            console.log('bestDateInfo:', priceComparator.bestDateInfo);

            const formattedDate = priceComparator.getFormattedDateForLabel();

            document.getElementById('titleDisplay').innerHTML = `
                <strong>标题显示结果：</strong>${formattedDate}<br>
                <small>这个日期应该与上面的"选中日期"完全一致</small><br>
                <small>bestDateInfo存在: ${priceComparator.bestDateInfo ? '是' : '否'}</small>
            `;

            console.log(`标题显示结果: ${formattedDate}`);
        }
    </script>
</body>
</html>
