<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜篮子智能比价工具</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🛒 菜篮子智能比价工具</h1>
            <p class="subtitle">比较销售价目表与菜篮子价格监测数据</p>
            <div class="help-section">
                <button id="helpBtn" class="help-btn">📖 使用说明</button>
                <button id="demoBtn" class="demo-btn">🎯 加载示例文件</button>
            </div>
        </header>

        <main>
            <!-- 文件上传区域 -->
            <section class="upload-section">
                <div class="upload-container">
                    <div class="upload-box" id="salesFileBox">
                        <div class="upload-icon">📊</div>
                        <h3>销售价目表</h3>
                        <p>上传销售价目表Excel文件</p>
                        <input type="file" id="salesFile" accept=".xlsx,.xls" hidden>
                        <button class="upload-btn" onclick="document.getElementById('salesFile').click()">
                            选择文件
                        </button>
                        <div class="file-info" id="salesFileInfo"></div>
                    </div>

                    <div class="upload-box" id="basketFileBox">
                        <div class="upload-icon">🥬</div>
                        <h3>菜篮子价格表</h3>
                        <p>上传菜篮子价格监测Excel文件</p>
                        <input type="file" id="basketFile" accept=".xlsx,.xls,.csv" hidden>
                        <button class="upload-btn" onclick="document.getElementById('basketFile').click()">
                            选择文件
                        </button>
                        <div class="backup-options" style="margin-top: 10px; font-size: 12px; color: #666; text-align: center;">
                            <p style="margin: 5px 0;">💡 如果Excel文件有问题，可以尝试：</p>
                            <button class="backup-btn" onclick="priceComparisonTool.showCSVConverter()" style="font-size: 11px; padding: 4px 8px; margin: 2px; background: #f0f0f0; border: 1px solid #ccc; border-radius: 3px; cursor: pointer;">转换为CSV</button>
                            <button class="backup-btn" onclick="priceComparisonTool.showFileRepair()" style="font-size: 11px; padding: 4px 8px; margin: 2px; background: #f0f0f0; border: 1px solid #ccc; border-radius: 3px; cursor: pointer;">文件修复指南</button>
                        </div>
                        <div class="file-info" id="basketFileInfo"></div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="compareBtn" class="primary-btn" disabled>
                        🔍 开始比价分析
                    </button>
                    <button id="clearBtn" class="secondary-btn">
                        🗑️ 清空数据
                    </button>
                </div>
            </section>

            <!-- 进度指示器 -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">正在处理...</div>
                </div>
            </section>

            <!-- 比价结果区域 -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h2>📈 比价分析结果</h2>
                    <div class="results-actions">
                        <button id="exportBtn" class="export-btn">
                            📥 导出结果
                        </button>
                    </div>
                </div>

                <!-- 统计摘要 -->
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon">📊</div>
                        <div class="card-content">
                            <div class="card-number" id="totalMatched">0</div>
                            <div class="card-label">匹配商品数</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">💰</div>
                        <div class="card-content">
                            <div class="card-number" id="avgPriceDiff">0%</div>
                            <div class="card-label">平均价差</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">📅</div>
                        <div class="card-content">
                            <div class="card-number" id="matchedDate">-</div>
                            <div class="card-label" id="matchedDateLabel">选中日期价格</div>
                        </div>
                    </div>
                </div>

                <!-- 详细结果表格 -->
                <div class="table-container">
                    <table id="resultsTable">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>计价单位</th>
                                <th>销售价格</th>
                                <th>菜篮子价格</th>
                                <th>价格差异</th>
                                <th>差异百分比</th>
                                <th>匹配状态</th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody">
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 错误信息区域 -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <div class="error-content">
                        <h3>处理出错</h3>
                        <p id="errorMessage"></p>
                        <button id="retryBtn" class="retry-btn">重试</button>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2025 菜篮子智能比价工具 - 专业的价格分析解决方案</p>
        </footer>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在处理Excel文件...</div>
    </div>

    <!-- 帮助模态框 -->
    <div class="help-modal" id="helpModal" style="display: none;">
        <div class="help-content">
            <div class="help-header">
                <h3>📖 使用说明</h3>
                <button class="close-help">×</button>
            </div>
            <div class="help-body">
                <div class="help-section">
                    <h4>📋 功能介绍</h4>
                    <p>本工具用于比较销售价目表与菜篮子价格监测数据，自动匹配商品并计算价格差异。</p>
                </div>

                <div class="help-section">
                    <h4>📁 文件要求</h4>
                    <ul>
                        <li><strong>销售价目表：</strong>需包含"物料名称"、"计价单位"、"市场价"字段</li>
                        <li><strong>菜篮子价格表：</strong>需包含"商品/分类名称"、"计价单位"、日期相关的"本期"价格字段</li>
                        <li><strong>文件格式：</strong>支持 .xlsx 和 .xls 格式</li>
                        <li><strong>文件大小：</strong>不超过 10MB</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>🔍 匹配逻辑</h4>
                    <ul>
                        <li><strong>日期优先级：</strong>优先匹配25日数据，然后按26、24、27、23日顺序查找</li>
                        <li><strong>商品匹配：</strong>先精确匹配商品名称和单位，再进行模糊匹配</li>
                        <li><strong>价格计算：</strong>计算绝对差异和百分比差异</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>⌨️ 快捷键</h4>
                    <ul>
                        <li><kbd>Ctrl + O</kbd> - 打开文件</li>
                        <li><kbd>Ctrl + Enter</kbd> - 开始比价</li>
                        <li><kbd>Ctrl + S</kbd> - 导出结果</li>
                        <li><kbd>Esc</kbd> - 清空数据</li>
                    </ul>
                </div>

                <div class="help-section">
                    <h4>📊 结果说明</h4>
                    <ul>
                        <li><span class="status-demo exact">精确匹配</span> - 商品名称和单位完全匹配</li>
                        <li><span class="status-demo fuzzy">模糊匹配</span> - 商品名称相似，单位匹配</li>
                        <li><span class="status-demo no-match">未匹配</span> - 未找到对应商品</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <div class="debug-header" onclick="toggleDebugPanel()">
            <span>🐛 调试控制台</span>
            <span class="debug-toggle">▼</span>
        </div>
        <div class="debug-content" id="debugContent">
            <div class="debug-controls">
                <button onclick="clearDebugLog()">清空日志</button>
                <button onclick="downloadDebugLog()">下载日志</button>
            </div>
            <div class="debug-log" id="debugLog"></div>
        </div>
    </div>

    <script src="script.js"></script>
    <script>
        // 调试面板功能
        function toggleDebugPanel() {
            const content = document.getElementById('debugContent');
            const toggle = document.querySelector('.debug-toggle');
            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.textContent = '▲';
            } else {
                content.style.display = 'none';
                toggle.textContent = '▼';
            }
        }

        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function downloadDebugLog() {
            const log = document.getElementById('debugLog').textContent;
            const blob = new Blob([log], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'debug-log-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.txt';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 重写console.log来显示在调试面板中
        const originalLog = console.log;
        const debugLog = document.getElementById('debugLog');

        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg =>
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            const logEntry = document.createElement('div');
            logEntry.className = 'debug-entry';
            logEntry.innerHTML = `<span class="debug-time">${new Date().toLocaleTimeString()}</span> ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        };

        // 初始化时隐藏调试内容
        document.getElementById('debugContent').style.display = 'none';
    </script>
</body>
</html>
