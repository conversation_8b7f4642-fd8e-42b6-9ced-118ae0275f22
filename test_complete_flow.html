<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整流程测试 - 日期匹配策略</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
        .highlight {
            background-color: #fff3cd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 完整流程测试 - 日期匹配策略验证</h1>
        <p>测试整个日期匹配流程是否严格按照README.md中的策略执行</p>
        
        <div class="test-section">
            <h3>📋 README.md 中的日期匹配策略</h3>
            <div class="test-result info">
                <strong>正确的优先级顺序：</strong><br>
                1. <strong>第1优先</strong>：25日栏目下的"本期"价格<br>
                2. <strong>第2优先</strong>：26日栏目下的"本期"价格<br>
                3. <strong>第3优先</strong>：27日栏目下的"本期"价格<br>
                4. <strong>第4优先</strong>：28日栏目下的"本期"价格<br>
                5. <strong>第5优先</strong>：29日栏目下的"本期"价格<br>
                6. 以此类推...
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 模拟菜篮子价格表数据</h3>
            <button onclick="testCompleteFlow()">运行完整流程测试</button>
            <div id="completeFlowResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 不同场景测试</h3>
            <button onclick="testDifferentScenarios()">测试多种场景</button>
            <div id="scenarioTestResult"></div>
        </div>
    </div>

    <script>
        // 复制主文件中的关键函数
        function getDatePriority(day) {
            const priorityOrder = [25, 26, 27, 28, 29];
            const index = priorityOrder.indexOf(day);
            if (index !== -1) {
                return index;
            }
            const distance = Math.abs(day - 25);
            return 10 + distance;
        }

        function findDateColumns(columns) {
            const dateColumns = [];
            const combinedDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日[-_]?(.*)/;
            const datePattern = /(\d{1,2})月(\d{1,2})日/;
            const fullDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;

            for (let i = 0; i < columns.length; i++) {
                const col = columns[i];
                if (!col) continue;

                // 优先查找组合格式的日期列（如 "2025年06月26日-本期"）
                if (combinedDatePattern.test(col)) {
                    const match = col.match(combinedDatePattern);
                    if (match) {
                        const day = parseInt(match[3]);
                        const priceType = match[4];
                        const priority = priceType.includes('本期') ?
                            getDatePriority(day) - 100 :
                            getDatePriority(day);

                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: day,
                            year: parseInt(match[1]),
                            month: parseInt(match[2]),
                            priceType: priceType,
                            priority: priority,
                            isCurrentPeriod: priceType.includes('本期'),
                            displayName: priceType.includes('本期') ? `${parseInt(match[2])}月${day}日本期` : col
                        });
                    }
                }
                // 查找包含"本期"的日期列
                else if (datePattern.test(col) && col.includes('本期')) {
                    const match = col.match(datePattern);
                    if (match) {
                        const day = parseInt(match[2]);
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: day,
                            month: parseInt(match[1]),
                            priority: getDatePriority(day) - 50,
                            isCurrentPeriod: true,
                            displayName: `${parseInt(match[1])}月${day}日本期`
                        });
                    }
                }
            }

            return dateColumns.sort((a, b) => a.priority - b.priority);
        }

        function testCompleteFlow() {
            const resultDiv = document.getElementById('completeFlowResult');
            
            // 模拟真实的菜篮子价格表列名（包含多个日期）
            const mockColumns = [
                '商品/分类名称',
                '规格等级', 
                '产地品牌',
                '计价单位',
                '6月24日本期',  // 应该是较低优先级
                '6月25日本期',  // 应该是最高优先级
                '6月26日本期',  // 应该是第二优先级
                '6月27日本期',  // 应该是第三优先级
                '6月28日本期',  // 应该是第四优先级
                '6月29日本期',  // 应该是第五优先级
                '6月30日本期'   // 应该是较低优先级
            ];

            console.log('🔍 开始完整流程测试...');
            console.log('输入列名:', mockColumns);

            // 执行日期列查找
            const dateColumns = findDateColumns(mockColumns);
            console.log('找到的日期列:', dateColumns);

            // 选择最佳日期列
            const bestDateColumn = dateColumns[0];
            
            let results = [];
            results.push('<h4>🔍 完整流程测试结果：</h4>');
            
            // 显示所有找到的日期列
            results.push('<table>');
            results.push('<tr><th>排序</th><th>列名</th><th>日期</th><th>优先级</th><th>显示名称</th><th>是否本期</th></tr>');
            dateColumns.forEach((col, index) => {
                const rowClass = index === 0 ? 'highlight' : '';
                results.push(`
                    <tr class="${rowClass}">
                        <td>${index + 1}</td>
                        <td>${col.column}</td>
                        <td>${col.day}日</td>
                        <td>${col.priority}</td>
                        <td>${col.displayName || col.column}</td>
                        <td>${col.isCurrentPeriod ? '是' : '否'}</td>
                    </tr>
                `);
            });
            results.push('</table>');

            // 验证结果
            const expectedDay = 25;
            const actualDay = bestDateColumn.day;
            const isCorrect = actualDay === expectedDay;
            
            const resultClass = isCorrect ? 'success' : 'error';
            const resultIcon = isCorrect ? '✅' : '❌';
            
            results.push(`
                <div class="test-result ${resultClass}">
                    ${resultIcon} <strong>最终选择结果:</strong><br>
                    <strong>选中列名:</strong> ${bestDateColumn.column}<br>
                    <strong>选中日期:</strong> ${actualDay}日<br>
                    <strong>显示名称:</strong> ${bestDateColumn.displayName}<br>
                    <strong>优先级:</strong> ${bestDateColumn.priority}<br>
                    <strong>验证结果:</strong> ${isCorrect ? '✅ 正确选择了25日（最高优先级）' : `❌ 错误！应该选择25日，但选择了${actualDay}日`}
                </div>
            `);

            // 验证优先级顺序
            const expectedOrder = [25, 26, 27, 28, 29, 24, 30];
            const actualOrder = dateColumns.map(col => col.day);
            const orderCorrect = JSON.stringify(expectedOrder) === JSON.stringify(actualOrder);
            
            const orderClass = orderCorrect ? 'success' : 'warning';
            const orderIcon = orderCorrect ? '✅' : '⚠️';
            
            results.push(`
                <div class="test-result ${orderClass}">
                    ${orderIcon} <strong>优先级顺序验证:</strong><br>
                    <strong>期望顺序:</strong> ${expectedOrder.join('日, ')}日<br>
                    <strong>实际顺序:</strong> ${actualOrder.join('日, ')}日<br>
                    <strong>验证结果:</strong> ${orderCorrect ? '✅ 优先级顺序完全正确' : '⚠️ 优先级顺序需要检查'}
                </div>
            `);

            resultDiv.innerHTML = results.join('');
        }

        function testDifferentScenarios() {
            const resultDiv = document.getElementById('scenarioTestResult');
            
            const scenarios = [
                {
                    name: '场景1：只有25日和26日',
                    columns: ['商品名称', '计价单位', '6月25日本期', '6月26日本期'],
                    expectedDay: 25
                },
                {
                    name: '场景2：缺少25日，有26日和27日',
                    columns: ['商品名称', '计价单位', '6月26日本期', '6月27日本期'],
                    expectedDay: 26
                },
                {
                    name: '场景3：只有28日和29日',
                    columns: ['商品名称', '计价单位', '6月28日本期', '6月29日本期'],
                    expectedDay: 28
                },
                {
                    name: '场景4：乱序排列',
                    columns: ['商品名称', '6月30日本期', '6月25日本期', '6月27日本期', '6月26日本期'],
                    expectedDay: 25
                },
                {
                    name: '场景5：包含非优先级日期',
                    columns: ['商品名称', '6月20日本期', '6月25日本期', '6月31日本期'],
                    expectedDay: 25
                }
            ];

            let results = [];
            results.push('<h4>📊 多场景测试结果：</h4>');
            
            scenarios.forEach((scenario, index) => {
                const dateColumns = findDateColumns(scenario.columns);
                const bestDate = dateColumns[0];
                const actualDay = bestDate ? bestDate.day : null;
                const isCorrect = actualDay === scenario.expectedDay;
                
                const resultClass = isCorrect ? 'success' : 'error';
                const resultIcon = isCorrect ? '✅' : '❌';
                
                results.push(`
                    <div class="test-result ${resultClass}">
                        <strong>${scenario.name}</strong><br>
                        <strong>输入列:</strong> ${scenario.columns.filter(col => col.includes('日')).join(', ')}<br>
                        <strong>期望选择:</strong> ${scenario.expectedDay}日<br>
                        <strong>实际选择:</strong> ${actualDay ? actualDay + '日' : '无'}<br>
                        <strong>结果:</strong> ${resultIcon} ${isCorrect ? '正确' : '错误'}
                    </div>
                `);
            });

            resultDiv.innerHTML = results.join('');
        }
    </script>
</body>
</html>
