#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re
from datetime import datetime

def analyze_basket_file():
    """分析菜篮子价格表的日期结构"""
    
    file_path = "广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx"
    
    print("=== 分析菜篮子价格表日期结构 ===")
    print(f"文件: {file_path}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path, header=None)
        print(f"数据形状: {df.shape}")
        
        # 查找日期行（通常在第3行，索引为2）
        print("\n=== 查找日期行 ===")
        for i in range(min(10, len(df))):
            row = df.iloc[i]
            print(f"第{i+1}行: {list(row[:10])}")  # 只显示前10列
            
            # 检查是否包含日期
            date_count = 0
            for cell in row:
                if pd.notna(cell) and isinstance(cell, str):
                    if re.search(r'2025年\d{2}月\d{2}日', str(cell)):
                        date_count += 1
            
            if date_count > 0:
                print(f"  ↑ 这一行包含 {date_count} 个日期")
        
        # 专门分析第3行（日期行）
        print("\n=== 第3行详细分析（日期行）===")
        if len(df) >= 3:
            date_row = df.iloc[2]  # 第3行，索引为2
            dates_found = []
            
            for i, cell in enumerate(date_row):
                if pd.notna(cell) and isinstance(cell, str):
                    # 查找完整日期格式
                    date_match = re.search(r'2025年(\d{2})月(\d{2})日', str(cell))
                    if date_match:
                        month = int(date_match.group(1))
                        day = int(date_match.group(2))
                        dates_found.append({
                            'column_index': i,
                            'cell_value': cell,
                            'month': month,
                            'day': day,
                            'date_str': f"2025年{month:02d}月{day:02d}日"
                        })
                        print(f"列{i}: {cell} -> {month}月{day}日")
            
            print(f"\n找到的所有日期 ({len(dates_found)}个):")
            for date_info in dates_found:
                print(f"  {date_info['date_str']} (列{date_info['column_index']})")
            
            # 检查是否有25日
            has_25th = any(d['day'] == 25 for d in dates_found)
            print(f"\n是否包含25日数据: {'是' if has_25th else '否'}")
            
            if not has_25th:
                print("按照优先级顺序查找可用日期:")
                priority_days = [25, 26, 27, 28, 29, 24, 23, 22, 21, 20]
                for day in priority_days:
                    matching_dates = [d for d in dates_found if d['day'] == day]
                    if matching_dates:
                        print(f"  OK {day}日: 可用 (列{matching_dates[0]['column_index']})")
                        break
                    else:
                        print(f"  NO {day}日: 不可用")
        
        # 分析第4行（本期/上期标识）
        print("\n=== 第4行详细分析（本期/上期标识）===")
        if len(df) >= 4:
            period_row = df.iloc[3]  # 第4行，索引为3
            
            for i, cell in enumerate(period_row):
                if pd.notna(cell) and isinstance(cell, str):
                    if '本期' in str(cell) or '上期' in str(cell):
                        print(f"列{i}: {cell}")
        
        return dates_found
        
    except Exception as e:
        print(f"分析失败: {e}")
        return []

if __name__ == "__main__":
    dates = analyze_basket_file()
    
    print(f"\n=== 总结 ===")
    print(f"共找到 {len(dates)} 个日期")
    if dates:
        print("可用日期列表:")
        for date_info in sorted(dates, key=lambda x: x['day']):
            print(f"  {date_info['day']}日 - {date_info['date_str']}")
