<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜篮子智能比价工具 v2.1</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🛒 菜篮子智能比价工具 v2.1</h1>
            <p class="subtitle">智能比较销售价目表与菜篮子价格表，快速发现价格差异</p>
        </header>

        <main class="main-content">
            <!-- 文件上传区域 -->
            <section class="upload-section">
                <div class="upload-container">
                    <div class="upload-item">
                        <label for="salesFile" class="upload-label">
                            <span class="upload-icon">📊</span>
                            <span class="upload-text">选择销售价目表</span>
                            <span class="upload-hint">支持 .xlsx 格式</span>
                        </label>
                        <input type="file" id="salesFile" accept=".xlsx,.xls" class="file-input">
                        <div class="file-info" id="salesFileInfo"></div>
                    </div>

                    <div class="upload-item">
                        <label for="basketFile" class="upload-label">
                            <span class="upload-icon">🛒</span>
                            <span class="upload-text">选择菜篮子价格表</span>
                            <span class="upload-hint">支持 .xlsx 格式</span>
                        </label>
                        <input type="file" id="basketFile" accept=".xlsx,.xls" class="file-input">
                        <div class="file-info" id="basketFileInfo"></div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="compareBtn" class="btn btn-primary" disabled>
                        <span class="btn-icon">🔍</span>
                        开始比价分析
                    </button>
                    <button id="clearBtn" class="btn btn-secondary">
                        <span class="btn-icon">🗑️</span>
                        清空数据
                    </button>
                </div>
            </section>

            <!-- 进度条 -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备开始...</div>
                </div>
            </section>

            <!-- 统计信息 -->
            <section class="stats-section" id="statsSection" style="display: none;">
                <h2>📊 比价分析结果</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">📦</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalMatched">0</div>
                            <div class="stat-label">匹配商品数</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <div class="stat-number" id="avgPriceDiff">0%</div>
                            <div class="stat-label">平均价差</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="matchedDateLabel">选中日期价格</div>
                            <div class="stat-label" id="matchedDate">未知</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 筛选选项 -->
            <section class="filter-section" id="filterSection" style="display: none;">
                <h3>🔍 筛选选项</h3>
                <div class="filter-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="showMatched" checked>
                        <span class="checkmark"></span>
                        显示已匹配
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="showUnmatched" checked>
                        <span class="checkmark"></span>
                        显示未匹配
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="showPriceDiff" checked>
                        <span class="checkmark"></span>
                        显示价格差异
                    </label>
                    <select id="sortBy" class="sort-select">
                        <option value="name">按商品名称排序</option>
                        <option value="priceDiff">按价格差异排序</option>
                        <option value="salesPrice">按销售价格排序</option>
                        <option value="basketPrice">按菜篮子价格排序</option>
                    </select>
                </div>
            </section>

            <!-- 结果表格 -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h3>📋 比价分析详情</h3>
                    <button id="exportBtn" class="btn btn-export">
                        <span class="btn-icon">📤</span>
                        导出结果
                    </button>
                </div>
                <div class="table-container">
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>商品名称</th>
                                <th>计价单位</th>
                                <th>销售价格</th>
                                <th>菜篮子价格</th>
                                <th>价格差异</th>
                                <th>差异百分比</th>
                                <th>匹配状态</th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody">
                            <!-- 动态生成的结果行 -->
                        </tbody>
                    </table>
                </div>
            </section>
        </main>

        <!-- 错误提示 -->
        <div class="error-message" id="errorMessage" style="display: none;">
            <span class="error-icon">⚠️</span>
            <span class="error-text" id="errorText"></span>
            <button class="error-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>

        <!-- 成功提示 -->
        <div class="success-message" id="successMessage" style="display: none;">
            <span class="success-icon">✅</span>
            <span class="success-text" id="successText"></span>
            <button class="success-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>

        <!-- 加载遮罩 -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loadingText">正在处理...</div>
        </div>

        <!-- 调试面板 -->
        <div class="debug-panel">
            <div class="debug-toggle" onclick="toggleDebugPanel()">🐛</div>
            <div class="debug-content" id="debugContent">
                <h4>调试信息</h4>
                <div class="debug-stats">
                    <div>销售数据行数: <span id="debugSalesCount">0</span></div>
                    <div>菜篮子数据行数: <span id="debugBasketCount">0</span></div>
                    <div>匹配成功数: <span id="debugMatchedCount">0</span></div>
                    <div>匹配率: <span id="debugMatchRate">0%</span></div>
                    <div>平均价差: <span id="debugAvgDiff">0%</span></div>
                </div>
                <div class="debug-log" id="debugLog"></div>
            </div>
        </div>
    </div>

    <script>
        // 使用当前时间戳强制重新加载JavaScript
        const timestamp = new Date().getTime();
        const script = document.createElement('script');
        script.src = 'script.js?v=2.1&t=' + timestamp;
        document.head.appendChild(script);
    </script>
    <script>
        // 调试面板功能
        function toggleDebugPanel() {
            const content = document.getElementById('debugContent');
            const toggle = document.querySelector('.debug-toggle');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggle.style.backgroundColor = '#ff6b6b';
            } else {
                content.style.display = 'none';
                toggle.style.backgroundColor = '#4ecdc4';
            }
        }

        // 页面加载完成后初始化工具
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌟 页面加载完成，初始化比价工具...');
            window.priceComparisonTool = new PriceComparisonTool();
        });
    </script>
</body>
</html>
