<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期优先级测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📅 日期优先级匹配测试</h1>
        <p>测试日期匹配策略是否严格按照README.md中的规则执行：25日→26日→27日→28日→29日...</p>
        
        <div class="test-section">
            <h3>🎯 测试日期优先级函数</h3>
            <button onclick="testDatePriority()">运行测试</button>
            <div id="priorityTestResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试日期列排序</h3>
            <button onclick="testDateColumnSorting()">运行测试</button>
            <div id="sortingTestResult"></div>
        </div>

        <div class="test-section">
            <h3>🔍 测试实际数据匹配</h3>
            <button onclick="testRealDataMatching()">运行测试</button>
            <div id="realDataTestResult"></div>
        </div>
    </div>

    <script>
        // 复制主文件中的getDatePriority函数
        function getDatePriority(day) {
            // 严格按照README.md中的日期匹配策略：25日→26日→27日→28日→29日...
            const priorityOrder = [25, 26, 27, 28, 29];

            const index = priorityOrder.indexOf(day);
            if (index !== -1) {
                return index; // 返回在优先级数组中的位置，越小优先级越高
            }

            // 如果日期不在预定义列表中，给予较低优先级
            // 按照距离25日的远近来排序，但优先级低于前5个日期
            const distance = Math.abs(day - 25);
            return 10 + distance; // 确保优先级低于前5个日期
        }

        function testDatePriority() {
            const resultDiv = document.getElementById('priorityTestResult');
            let results = [];
            
            // 测试用例
            const testCases = [
                { day: 25, expectedPriority: 0, description: "25日应该是最高优先级" },
                { day: 26, expectedPriority: 1, description: "26日应该是第二优先级" },
                { day: 27, expectedPriority: 2, description: "27日应该是第三优先级" },
                { day: 28, expectedPriority: 3, description: "28日应该是第四优先级" },
                { day: 29, expectedPriority: 4, description: "29日应该是第五优先级" },
                { day: 24, expectedPriority: 11, description: "24日应该是较低优先级" },
                { day: 30, expectedPriority: 15, description: "30日应该是较低优先级" }
            ];

            results.push('<h4>📋 测试结果：</h4>');
            let allPassed = true;

            testCases.forEach(testCase => {
                const actualPriority = getDatePriority(testCase.day);
                const passed = actualPriority === testCase.expectedPriority;
                allPassed = allPassed && passed;
                
                const status = passed ? '✅' : '❌';
                const className = passed ? 'success' : 'error';
                
                results.push(`
                    <div class="test-result ${className}">
                        ${status} ${testCase.description}<br>
                        期望优先级: ${testCase.expectedPriority}, 实际优先级: ${actualPriority}
                    </div>
                `);
            });

            // 总结
            const summaryClass = allPassed ? 'success' : 'error';
            const summaryIcon = allPassed ? '🎉' : '⚠️';
            results.push(`
                <div class="test-result ${summaryClass}">
                    ${summaryIcon} <strong>测试总结: ${allPassed ? '所有测试通过' : '部分测试失败'}</strong>
                </div>
            `);

            resultDiv.innerHTML = results.join('');
        }

        function testDateColumnSorting() {
            const resultDiv = document.getElementById('sortingTestResult');
            
            // 模拟日期列数据
            const mockDateColumns = [
                { column: '6月30日本期', day: 30, priority: getDatePriority(30) },
                { column: '6月25日本期', day: 25, priority: getDatePriority(25) },
                { column: '6月28日本期', day: 28, priority: getDatePriority(28) },
                { column: '6月26日本期', day: 26, priority: getDatePriority(26) },
                { column: '6月27日本期', day: 27, priority: getDatePriority(27) },
                { column: '6月24日本期', day: 24, priority: getDatePriority(24) },
                { column: '6月29日本期', day: 29, priority: getDatePriority(29) }
            ];

            // 排序前
            const beforeSorting = mockDateColumns.map(col => `${col.day}日(优先级:${col.priority})`).join(', ');
            
            // 按优先级排序
            const sortedColumns = [...mockDateColumns].sort((a, b) => a.priority - b.priority);
            
            // 排序后
            const afterSorting = sortedColumns.map(col => `${col.day}日(优先级:${col.priority})`).join(', ');
            
            // 期望的顺序
            const expectedOrder = [25, 26, 27, 28, 29, 24, 30];
            const actualOrder = sortedColumns.map(col => col.day);
            
            const isCorrect = JSON.stringify(expectedOrder) === JSON.stringify(actualOrder);
            const resultClass = isCorrect ? 'success' : 'error';
            const resultIcon = isCorrect ? '✅' : '❌';

            resultDiv.innerHTML = `
                <div class="test-result info">
                    <strong>排序前:</strong> ${beforeSorting}
                </div>
                <div class="test-result info">
                    <strong>排序后:</strong> ${afterSorting}
                </div>
                <div class="test-result ${resultClass}">
                    ${resultIcon} <strong>排序结果: ${isCorrect ? '正确' : '错误'}</strong><br>
                    期望顺序: ${expectedOrder.join('日, ')}日<br>
                    实际顺序: ${actualOrder.join('日, ')}日
                </div>
            `;
        }

        function testRealDataMatching() {
            const resultDiv = document.getElementById('realDataTestResult');
            
            // 模拟真实的菜篮子价格表列名
            const realColumnNames = [
                '商品/分类名称',
                '规格等级', 
                '产地品牌',
                '计价单位',
                '6月24日本期',
                '6月25日本期', 
                '6月26日本期',
                '6月27日本期',
                '6月28日本期',
                '6月29日本期',
                '6月30日本期'
            ];

            // 提取日期列并计算优先级
            const dateColumns = [];
            realColumnNames.forEach((col, index) => {
                const match = col.match(/(\d{1,2})月(\d{1,2})日.*本期/);
                if (match) {
                    const day = parseInt(match[2]);
                    dateColumns.push({
                        column: col,
                        columnIndex: index,
                        day: day,
                        priority: getDatePriority(day)
                    });
                }
            });

            // 按优先级排序
            const sortedDateColumns = dateColumns.sort((a, b) => a.priority - b.priority);
            
            // 选择最佳日期列
            const bestDateColumn = sortedDateColumns[0];
            
            const results = [];
            results.push('<h4>📊 真实数据匹配测试结果：</h4>');
            results.push(`<div class="test-result info"><strong>发现的日期列:</strong><br>${dateColumns.map(col => `${col.column} (优先级: ${col.priority})`).join('<br>')}</div>`);
            results.push(`<div class="test-result info"><strong>排序后的日期列:</strong><br>${sortedDateColumns.map(col => `${col.column} (优先级: ${col.priority})`).join('<br>')}</div>`);
            
            const isCorrect = bestDateColumn.day === 25;
            const resultClass = isCorrect ? 'success' : 'error';
            const resultIcon = isCorrect ? '✅' : '❌';
            
            results.push(`
                <div class="test-result ${resultClass}">
                    ${resultIcon} <strong>选中的最佳日期列:</strong> ${bestDateColumn.column}<br>
                    <strong>日期:</strong> ${bestDateColumn.day}日<br>
                    <strong>优先级:</strong> ${bestDateColumn.priority}<br>
                    <strong>结果:</strong> ${isCorrect ? '正确选择了25日' : '选择错误，应该选择25日'}
                </div>
            `);

            resultDiv.innerHTML = results.join('');
        }
    </script>
</body>
</html>
