#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re

def debug_date_matching():
    """调试日期匹配过程"""
    
    file_path = "广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx"
    
    print("=== 调试日期匹配过程 ===")
    print(f"文件: {file_path}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path, header=None)
        print(f"数据形状: {df.shape}")
        
        # 查找日期行（第3行，索引为2）
        print("\n=== 分析第3行（日期行）===")
        if len(df) >= 3:
            date_row = df.iloc[2]  # 第3行，索引为2
            print(f"第3行内容: {list(date_row)}")
            
            # 查找所有日期
            dates_found = []
            for i, cell in enumerate(date_row):
                if pd.notna(cell) and isinstance(cell, str):
                    date_match = re.search(r'2025年(\d{2})月(\d{2})日', str(cell))
                    if date_match:
                        month = int(date_match.group(1))
                        day = int(date_match.group(2))
                        dates_found.append({
                            'column_index': i,
                            'cell_value': cell,
                            'month': month,
                            'day': day
                        })
            
            print(f"\n找到的日期 ({len(dates_found)}个):")
            for date_info in dates_found:
                print(f"  列{date_info['column_index']}: {date_info['cell_value']} -> {date_info['month']}月{date_info['day']}日")
        
        # 查找本期行（第4行，索引为3）
        print("\n=== 分析第4行（本期行）===")
        if len(df) >= 4:
            period_row = df.iloc[3]  # 第4行，索引为3
            print(f"第4行内容: {list(period_row)}")
            
            # 查找所有"本期"
            benqi_found = []
            for i, cell in enumerate(period_row):
                if pd.notna(cell) and isinstance(cell, str) and '本期' in str(cell):
                    benqi_found.append({
                        'column_index': i,
                        'cell_value': cell
                    })
            
            print(f"\n找到的本期标识 ({len(benqi_found)}个):")
            for benqi_info in benqi_found:
                print(f"  列{benqi_info['column_index']}: {benqi_info['cell_value']}")
        
        # 模拟JavaScript的日期匹配逻辑
        print("\n=== 模拟JavaScript日期匹配逻辑 ===")
        
        # 优先级顺序：25日 > 26日 > 27日 > 28日 > 29日
        priority_days = [25, 26, 27, 28, 29]
        
        selected_date_column = -1
        selected_date = ''
        
        print("按优先级查找日期列...")
        for priority_day in priority_days:
            print(f"\n查找 {priority_day}日...")

            # 在日期行中查找该日期
            for date_info in dates_found:
                if date_info['day'] == priority_day:
                    print(f"  OK 在列{date_info['column_index']}找到{priority_day}日: {date_info['cell_value']}")

                    # 查找该日期对应的"本期"列（通常在日期列后1-2列内）
                    for offset in range(3):  # 检查0, 1, 2偏移
                        check_col = date_info['column_index'] + offset
                        if check_col < len(period_row) and pd.notna(period_row.iloc[check_col]):
                            period_cell = str(period_row.iloc[check_col])
                            print(f"    检查列{check_col}: {period_cell}")

                            if '本期' in period_cell:
                                selected_date_column = check_col
                                selected_date = f"{priority_day}日"
                                print(f"    FOUND 找到本期列! 列{check_col}: {period_cell}")
                                break

                    if selected_date_column != -1:
                        break

            if selected_date_column != -1:
                print(f"SUCCESS 选择了 {priority_day}日 (列{selected_date_column})")
                break
            else:
                print(f"  NO {priority_day}日不可用")
        
        if selected_date_column == -1:
            print("ERROR 未找到符合优先级的日期列")
        else:
            print(f"\n=== 最终结果 ===")
            print(f"选中的日期: {selected_date}")
            print(f"选中的列: {selected_date_column}")
            
            # 显示该列的一些数据
            print(f"\n该列的前10行数据:")
            for i in range(min(10, len(df))):
                if selected_date_column < len(df.iloc[i]):
                    cell_value = df.iloc[i, selected_date_column]
                    print(f"  第{i+1}行: {cell_value}")
        
        return selected_date_column, selected_date
        
    except Exception as e:
        print(f"调试失败: {e}")
        return -1, ''

if __name__ == "__main__":
    col, date = debug_date_matching()
    print(f"\n=== 总结 ===")
    print(f"选中列: {col}")
    print(f"选中日期: {date}")
