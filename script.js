// 菜篮子智能比价工具 - 主要JavaScript逻辑
console.log('🔄 JavaScript文件已加载 - v2.1 日期显示修复版');
console.log('⏰ 加载时间:', new Date().toLocaleTimeString());

class PriceComparisonTool {
    constructor() {
        this.salesData = null;
        this.basketData = null;
        this.comparisonResults = [];
        this.statistics = {
            totalMatched: 0,
            avgPriceDiff: 0,
            matchedDate: ''
        };
        this.bestDateInfo = null; // 保存最佳日期信息
        this.salesFileName = '';
        this.basketFileName = '';
        this.init();
    }

    init() {
        console.log('🚀 菜篮子智能比价工具 v2.1 - 日期显示修复版');
        console.log('Initializing PriceComparisonTool...');

        // 检查XLSX库是否加载
        if (typeof XLSX === 'undefined') {
            console.error('XLSX library not loaded!');
            this.showError('Excel处理库未加载，请刷新页面重试');
            return;
        }
        console.log('XLSX library loaded successfully');

        this.bindEvents();
        this.setupFileHandlers();
        this.setupKeyboardShortcuts();
        this.animateProgress();

        console.log('PriceComparisonTool initialization complete');
    }

    bindEvents() {
        console.log('Binding events...');

        try {
            // 检查元素是否存在
            const salesFile = document.getElementById('salesFile');
            const basketFile = document.getElementById('basketFile');

            if (!salesFile) {
                console.warn('salesFile element not found, skipping file upload events');
            } else {
                // 文件上传事件
                salesFile.addEventListener('change', (e) => {
                    console.log('Sales file change event triggered');
                    this.handleSalesFileUpload(e);
                });
            }

            if (!basketFile) {
                console.warn('basketFile element not found, skipping file upload events');
            } else {
                basketFile.addEventListener('change', (e) => {
                    console.log('Basket file change event triggered');
                    this.handleBasketFileUpload(e);
                });
            }

            // 按钮事件
            const compareBtn = document.getElementById('compareBtn');
            const clearBtn = document.getElementById('clearBtn');
            const exportBtn = document.getElementById('exportBtn');
            const retryBtn = document.getElementById('retryBtn');
            const helpBtn = document.getElementById('helpBtn');
            const demoBtn = document.getElementById('demoBtn');

            if (compareBtn) compareBtn.addEventListener('click', () => this.startComparison());
            if (clearBtn) clearBtn.addEventListener('click', () => this.clearData());
            if (exportBtn) exportBtn.addEventListener('click', () => this.exportResults());
            if (retryBtn) retryBtn.addEventListener('click', () => this.hideError());
            if (helpBtn) helpBtn.addEventListener('click', () => this.showHelp());
            if (demoBtn) demoBtn.addEventListener('click', () => this.loadDemoFiles());

            console.log('Events bound successfully');
        } catch (error) {
            console.error('Error binding events:', error);
        }
    }

    setupFileHandlers() {
        // 拖拽上传支持
        this.setupDragAndDrop('salesFileBox', 'salesFile');
        this.setupDragAndDrop('basketFileBox', 'basketFile');
    }

    setupDragAndDrop(boxId, inputId) {
        const box = document.getElementById(boxId);
        const input = document.getElementById(inputId);

        if (!box || !input) {
            console.warn(`Drag and drop setup skipped: ${boxId} or ${inputId} not found`);
            return;
        }

        box.addEventListener('dragover', (e) => {
            e.preventDefault();
            box.style.borderColor = '#3498db';
            box.style.background = '#f8f9fa';
        });

        box.addEventListener('dragleave', (e) => {
            e.preventDefault();
            box.style.borderColor = '#bdc3c7';
            box.style.background = '#fff';
        });

        box.addEventListener('drop', (e) => {
            e.preventDefault();
            box.style.borderColor = '#bdc3c7';
            box.style.background = '#fff';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                input.files = files;
                input.dispatchEvent(new Event('change'));
            }
        });
    }

    async handleSalesFileUpload(event) {
        console.log('Sales file upload triggered');
        const file = event.target.files[0];
        if (!file) {
            console.log('No file selected');
            return;
        }

        console.log('File selected:', file.name, file.size, file.type);

        // 显示简单的处理提示
        const infoElement = document.getElementById('salesFileInfo');
        if (infoElement) {
            infoElement.innerHTML = '正在处理文件...';
            infoElement.classList.add('show');
        }

        try {
            console.log('Starting file parsing...');
            this.salesData = await this.parseExcelFile(file);
            console.log('File parsed successfully, rows:', this.salesData.length);

            if (!this.salesData || this.salesData.length === 0) {
                throw new Error('文件数据为空');
            }

            // 更新UI
            this.updateFileInfo('salesFileInfo', file.name, this.salesData.length, this.salesData);
            this.updateUploadBox('salesFileBox', true);
            this.checkReadyToCompare();

            console.log('Sales file processing complete');

        } catch (error) {
            console.error('Error processing sales file:', error);
            if (infoElement) {
                infoElement.innerHTML = `错误: ${error.message}`;
                infoElement.style.color = 'red';
            }
            this.updateUploadBox('salesFileBox', false);
            event.target.value = ''; // 清空文件输入
        }
    }

    async handleBasketFileUpload(event) {
        console.log('Basket file upload triggered');
        const file = event.target.files[0];
        if (!file) {
            console.log('No file selected');
            return;
        }

        console.log('File selected:', file.name, file.size, file.type);

        // 显示简单的处理提示
        const infoElement = document.getElementById('basketFileInfo');
        if (infoElement) {
            infoElement.innerHTML = '正在处理文件...';
            infoElement.classList.add('show');
        }

        try {
            console.log('Starting file parsing...');
            this.basketData = await this.parseExcelFile(file);
            this.basketFileName = file.name; // 保存文件名用于日期推断
            console.log('File parsed successfully, rows:', this.basketData.length);

            if (!this.basketData || this.basketData.length === 0) {
                throw new Error('文件数据为空');
            }

            // 更新UI
            this.updateFileInfo('basketFileInfo', file.name, this.basketData.length, this.basketData);
            this.updateUploadBox('basketFileBox', true);
            this.checkReadyToCompare();

            console.log('Basket file processing complete');

        } catch (error) {
            console.error('Error processing basket file:', error);
            if (infoElement) {
                infoElement.innerHTML = `错误: ${error.message}`;
                infoElement.style.color = 'red';
            }
            this.updateUploadBox('basketFileBox', false);
            event.target.value = ''; // 清空文件输入
        }
    }

    // 将列字母转换为数字 (A=1, B=2, ..., Z=26, AA=27, ...)
    columnToNumber(column) {
        let result = 0;
        for (let i = 0; i < column.length; i++) {
            result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
        }
        return result;
    }

    async parseExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    console.log('文件大小:', data.length, 'bytes');

                    // 使用更宽松的解析选项
                    const workbook = XLSX.read(data, {
                        type: 'array',
                        cellDates: true,
                        cellNF: false,
                        cellText: false,
                        raw: false,
                        defval: ''
                    });

                    console.log('工作表名称:', workbook.SheetNames);

                    // 检查所有工作表，寻找多列数据
                    let bestSheet = null;
                    let bestSheetName = null;
                    let maxColumns = 0;

                    for (let i = 0; i < workbook.SheetNames.length; i++) {
                        const sheetName = workbook.SheetNames[i];
                        const sheet = workbook.Sheets[sheetName];
                        const range = sheet['!ref'];

                        console.log(`工作表 ${i} "${sheetName}" 范围:`, range);

                        // 计算列数
                        let columnCount = 1;
                        if (range) {
                            const match = range.match(/([A-Z]+)\d+:([A-Z]+)\d+/);
                            if (match) {
                                const startCol = match[1];
                                const endCol = match[2];
                                columnCount = this.columnToNumber(endCol) - this.columnToNumber(startCol) + 1;
                            }
                        }

                        console.log(`工作表 "${sheetName}" 列数:`, columnCount);

                        // 检查前几个单元格的内容
                        const cellKeys = Object.keys(sheet).filter(key => key.match(/^[A-Z]+[0-9]+$/)).slice(0, 15);
                        console.log(`工作表 "${sheetName}" 单元格样本:`, cellKeys.map(key => ({
                            cell: key,
                            value: sheet[key].v,
                            type: sheet[key].t
                        })));

                        // 选择列数最多的工作表
                        if (columnCount > maxColumns) {
                            maxColumns = columnCount;
                            bestSheet = sheet;
                            bestSheetName = sheetName;
                        }
                    }

                    // 使用列数最多的工作表
                    const worksheet = bestSheet || workbook.Sheets[workbook.SheetNames[0]];
                    const firstSheetName = bestSheetName || workbook.SheetNames[0];

                    console.log('使用工作表:', firstSheetName);
                    console.log('工作表范围:', worksheet['!ref']);

                    // 尝试不同的转换方式
                    let jsonData;
                    try {
                        // 方式1: 使用header: 1 (数组格式)
                        jsonData = XLSX.utils.sheet_to_json(worksheet, {
                            header: 1,
                            defval: '',
                            blankrows: false,
                            raw: false
                        });
                        console.log('方式1解析结果 - 行数:', jsonData.length);
                        console.log('方式1解析结果 - 前3行:', jsonData.slice(0, 3));

                        // 检查是否所有行都只有一列
                        const columnCounts = jsonData.map(row => row ? row.length : 0);
                        console.log('每行列数:', columnCounts.slice(0, 10));

                        if (jsonData.length > 0 && jsonData.every(row => !row || row.length <= 1)) {
                            console.log('检测到单列数据，尝试分割处理...');

                            // 检查数据是否包含分隔符
                            const sampleData = jsonData.slice(0, 10).filter(row => row && row[0]);
                            console.log('单列数据样本:', sampleData);

                            let splitData = null;

                            // 尝试不同的分隔符
                            const separators = ['\t', ',', '|', ';', ' '];
                            for (const sep of separators) {
                                const testSplit = sampleData.map(row =>
                                    row[0] ? String(row[0]).split(sep) : []
                                ).filter(arr => arr.length > 1);

                                if (testSplit.length > 0) {
                                    console.log(`检测到分隔符 "${sep === '\t' ? '\\t' : sep}":`, testSplit.slice(0, 2));
                                    splitData = jsonData.map(row =>
                                        row && row[0] ? String(row[0]).split(sep) : []
                                    ).filter(arr => arr.length > 0);
                                    break;
                                }
                            }

                            if (splitData && splitData.length > 0) {
                                console.log('分割后的数据:', splitData.slice(0, 3));
                                jsonData = splitData;
                            } else {
                                console.log('尝试方式2: 直接读取对象格式...');
                                // 方式2: 直接读取对象格式
                                const objectData = XLSX.utils.sheet_to_json(worksheet, {
                                    defval: '',
                                    blankrows: false,
                                    raw: false
                                });
                                console.log('方式2解析结果:', objectData.slice(0, 3));

                                if (objectData.length > 0 && Object.keys(objectData[0]).length > 1) {
                                    // 转换回数组格式
                                    const headers = Object.keys(objectData[0]);
                                    jsonData = [headers, ...objectData.map(obj => headers.map(h => obj[h]))];
                                    console.log('转换后的数据:', jsonData.slice(0, 3));
                                } else {
                                    console.warn('无法解析多列数据，可能需要检查Excel文件格式');
                                }
                            }
                        }

                    } catch (parseError) {
                        console.error('JSON转换失败:', parseError);
                        throw parseError;
                    }

                    // 过滤空行并转换为对象数组
                    const processedData = this.processExcelData(jsonData);
                    resolve(processedData);
                } catch (error) {
                    console.error('Excel解析错误:', error);
                    reject(new Error('Excel文件格式不正确或已损坏: ' + error.message));
                }
            };
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsArrayBuffer(file);
        });
    }

    processExcelData(rawData) {
        if (!rawData || rawData.length < 2) {
            throw new Error('Excel文件数据不足');
        }

        console.log('Raw data preview:', rawData.slice(0, 10));

        // 检查是否是菜篮子价格表（更精确的识别逻辑）
        const isBasketPriceTable = rawData.some(row =>
            row && row.some(cell =>
                cell && typeof cell === 'string' &&
                (cell.includes('菜篮子价格监测') ||
                 cell.includes('监测日报表') ||
                 cell.includes('监测对比表') ||
                 (cell.includes('商品/分类名称') && rawData.some(r =>
                    r && r.some(c => c && c.toString().includes('监测'))
                 )))
            )
        );

        console.log('是否为菜篮子价格表:', isBasketPriceTable);

        if (isBasketPriceTable) {
            return this.parseBasketPriceTable(rawData);
        } else {
            return this.parseRegularTable(rawData);
        }
    }

    parseBasketPriceTable(rawData) {
        // 使用调试工具中成功的菜篮子价格表解析逻辑
        console.log('🛒 开始解析菜篮子价格表...');
        console.log('🛒 菜篮子价格表原始数据行数:', rawData.length);
        console.log('🛒 前5行原始数据:');
        rawData.slice(0, 5).forEach((row, index) => {
            console.log(`🛒 第${index + 1}行:`, row);
        });

        // 查找表头行 - 更灵活的识别逻辑
        let headerRowIndex = -1;
        let headers = [];

        console.log('🛒 开始查找菜篮子价格表表头行...');

        for (let i = 0; i < Math.min(10, rawData.length); i++) {
            const row = rawData[i];
            if (row && Array.isArray(row) && row.length > 0) {
                const rowText = row.join('').toLowerCase();
                console.log(`🛒 检查第${i + 1}行: "${rowText}"`);
                console.log(`🛒 第${i + 1}行原始数据:`, row);

                // 多种表头识别条件 - 根据README.md的格式要求
                const hasProductName = rowText.includes('商品/分类名称') || rowText.includes('商品分类名称') ||
                                     rowText.includes('物料名称') || rowText.includes('商品名称') ||
                                     rowText.includes('品名') || rowText.includes('商品');
                const hasUnit = rowText.includes('计价单位') || rowText.includes('单位');
                const hasDate = rowText.includes('月') && rowText.includes('日');
                const hasBenqi = rowText.includes('本期');

                console.log(`🛒 第${i + 1}行检查结果: 商品名称=${hasProductName}, 单位=${hasUnit}, 日期=${hasDate}, 本期=${hasBenqi}`);

                // 如果包含商品名称和单位，或者包含日期和本期，则认为是表头行
                if ((hasProductName && hasUnit) || (hasDate && hasBenqi) ||
                    (hasProductName && hasDate) || (hasUnit && hasDate)) {
                    headerRowIndex = i;
                    headers = row.map(cell => cell ? cell.toString().trim() : '').filter(cell => cell !== '');
                    console.log(`🛒 找到菜篮子价格表表头行 ${i + 1}:`, headers);
                    break;
                }
            }
        }

        if (headerRowIndex === -1) {
            console.error('❌ 未找到菜篮子价格表表头行');
            throw new Error('未找到菜篮子价格表表头行');
        }

        // 查找日期列 - 处理多行表头结构
        console.log('🔍 开始查找日期列（多行表头结构）...');

        // 获取日期行和本期行
        let dateRowIndex = -1;
        let periodRowIndex = -1;
        let dateRow = [];
        let periodRow = [];

        // 查找包含日期的行（通常在表头行后1-3行内）
        for (let i = headerRowIndex + 1; i < Math.min(headerRowIndex + 4, rawData.length); i++) {
            const row = rawData[i];
            if (row && Array.isArray(row)) {
                const rowText = row.join('').toLowerCase();
                console.log(`🔍 检查第${i + 1}行: "${rowText}"`);

                if (rowText.includes('2025年') && rowText.includes('月') && rowText.includes('日')) {
                    dateRowIndex = i;
                    dateRow = row;
                    console.log(`📅 找到日期行 ${i + 1}:`, dateRow);
                }

                if (rowText.includes('本期')) {
                    periodRowIndex = i;
                    periodRow = row;
                    console.log(`📊 找到本期行 ${i + 1}:`, periodRow);
                }
            }
        }

        if (dateRowIndex === -1 || periodRowIndex === -1) {
            console.error('❌ 未找到日期行或本期行');
            throw new Error('未找到日期行或本期行');
        }

        // 查找最佳日期列（优先级：25日 > 26日 > 27日 > 28日 > 29日）
        const datePriority = [
            {pattern: '2025年06月25日', name: '25日'},
            {pattern: '2025年06月26日', name: '26日'},
            {pattern: '2025年06月27日', name: '27日'},
            {pattern: '2025年06月28日', name: '28日'},
            {pattern: '2025年06月29日', name: '29日'}
        ];
        let selectedDateColumn = -1;
        let selectedDate = '';

        console.log('🎯 按优先级查找日期列...');
        for (const dateInfo of datePriority) {
            console.log(`🔍 查找日期: ${dateInfo.pattern}`);
            for (let col = 0; col < dateRow.length; col++) {
                const dateCell = dateRow[col];
                if (dateCell && dateCell.toString().includes(dateInfo.pattern)) {
                    console.log(`📅 在列${col + 1}找到日期: ${dateCell}`);

                    // 查找该日期对应的"本期"列（通常在日期列后1-2列内）
                    for (let offset = 0; offset <= 2; offset++) {
                        const checkCol = col + offset;
                        if (periodRow.length > checkCol && periodRow[checkCol] &&
                            periodRow[checkCol].toString().includes('本期')) {
                            selectedDateColumn = checkCol;
                            selectedDate = dateInfo.name;
                            console.log(`🎯 找到优先级日期列 ${checkCol + 1}: ${dateCell} -> ${periodRow[checkCol]} (${dateInfo.name})`);
                            break;
                        } else if (periodRow[checkCol]) {
                            console.log(`🔍 列${checkCol + 1}的本期行内容: ${periodRow[checkCol]}`);
                        }
                    }

                    if (selectedDateColumn !== -1) break;
                }
            }
            if (selectedDateColumn !== -1) break;
        }

        // 如果没找到优先级日期，查找任何包含"本期"的日期列
        if (selectedDateColumn === -1) {
            console.warn('⚠️ 未找到符合优先级的日期列，尝试查找任何包含"本期"的日期列');
            for (let col = 0; col < periodRow.length; col++) {
                const periodCell = periodRow[col];
                if (periodCell && periodCell.toString().includes('本期')) {
                    // 检查对应位置是否有日期
                    if (dateRow.length > col && dateRow[col] &&
                        dateRow[col].toString().includes('月')) {
                        selectedDateColumn = col;
                        selectedDate = '本期';
                        console.log(`📅 找到日期列 ${col + 1}: ${dateRow[col]} -> ${periodCell}`);
                        break;
                    }
                }
            }
        }

        if (selectedDateColumn === -1) {
            console.error('❌ 未找到有效的日期列');
            throw new Error('未找到有效的日期列');
        }

        // 转换为对象格式 - 从本期行之后开始解析数据
        const basketPriceData = [];
        const dataStartRow = Math.max(periodRowIndex + 1, headerRowIndex + 3); // 数据从本期行后开始

        console.log(`🛒 开始解析数据，从第${dataStartRow + 1}行开始...`);

        for (let i = dataStartRow; i < rawData.length; i++) {
            const row = rawData[i];
            if (row && Array.isArray(row) && row.length > 0) {
                const obj = {};

                // 使用基本表头构建对象
                for (let j = 0; j < headers.length && j < row.length; j++) {
                    if (headers[j] && headers[j].toString().trim() !== '') {
                        const key = headers[j].toString().trim();
                        const value = row[j] ? row[j].toString().trim() : '';
                        obj[key] = value;
                    }
                }

                // 添加选中日期列的价格数据
                if (selectedDateColumn !== -1 && row[selectedDateColumn]) {
                    obj['选中日期价格'] = row[selectedDateColumn].toString().trim();
                    obj['选中日期'] = selectedDate;
                }

                // 检查是否有有效的商品名称和价格
                const productName = obj['商品/分类名称'] || obj['商品分类名称'] || obj['物料名称'] || '';
                const unit = obj['计价单位'] || '';
                const price = obj['选中日期价格'] || '';

                if (productName && productName.trim() !== '' &&
                    unit && unit.trim() !== '' &&
                    price && price.toString().trim() !== '' &&
                    !isNaN(parseFloat(price))) {
                    basketPriceData.push(obj);
                }
            }
        }

        console.log(`🛒 菜篮子价格表解析完成，找到 ${basketPriceData.length} 行有效数据`);
        console.log('🛒 使用的日期列:', selectedDateColumn);
        console.log('🛒 前10行菜篮子价格数据:');
        basketPriceData.slice(0, 10).forEach((item, index) => {
            const productName = item['商品/分类名称'] || item['商品分类名称'] || item['物料名称'] || '';
            const unit = item['计价单位'] || '';
            const price = item['选中日期价格'] || '';
            console.log(`🛒 ${index + 1}. ${productName} | ${unit} | ${selectedDateColumn}: ${price}`);
        });

        return basketPriceData;
    }

    parseRegularTable(rawData) {
        // 常规表格处理
        console.log('解析常规表格...');

        let headerRowIndex = -1;
        const keyFields = ['物料名称', '商品', '名称', '计价单位', '单位', '价格', '市场价'];

        for (let i = 0; i < Math.min(rawData.length, 20); i++) {
            const row = rawData[i];
            if (row && Array.isArray(row)) {
                console.log(`检查第${i}行:`, row.slice(0, 15));

                const nonEmptyCells = row.filter(cell => cell && cell.toString().trim() !== '').length;
                console.log(`第${i}行非空列数: ${nonEmptyCells}`);

                if (nonEmptyCells >= 3) {
                    const hasKeyFields = row.some(cell =>
                        cell && typeof cell === 'string' &&
                        keyFields.some(field => cell.includes(field))
                    );

                    console.log(`第${i}行包含关键字段: ${hasKeyFields}`);

                    if (hasKeyFields) {
                        headerRowIndex = i;
                        console.log('Found header row at index:', i, 'Headers:', row);
                        break;
                    }
                }
            }
        }

        if (headerRowIndex === -1) {
            headerRowIndex = 0;
            console.log('No clear header found, using first row');
        }

        const headers = rawData[headerRowIndex];
        const dataRows = rawData.slice(headerRowIndex + 1);

        console.log('Using headers:', headers);
        console.log('Data rows count:', dataRows.length);

        const processedData = dataRows
            .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
            .map(row => {
                const obj = {};
                headers.forEach((header, index) => {
                    if (header) {
                        obj[header] = row[index] || '';
                    }
                });
                return obj;
            })
            .filter(obj => {
                // 过滤掉完全空的对象或只包含公司信息的行
                const values = Object.values(obj);
                const hasContent = values.some(val => val && String(val).trim().length > 0);
                const isCompanyInfo = values.some(val =>
                    val && String(val).includes('广东金勺子食品有限公司')
                );
                return hasContent && !isCompanyInfo;
            });

        console.log('常规表格处理完成，数据行数:', processedData.length);
        console.log('常规表格样本数据:', processedData.slice(0, 3));

        return processedData;
    }

    updateFileInfo(infoId, fileName, rowCount, data = null) {
        const infoElement = document.getElementById(infoId);
        let infoHtml = `
            <strong>文件名：</strong>${fileName}<br>
            <strong>数据行数：</strong>${rowCount} 行
        `;

        // 如果有数据，显示列信息
        if (data && data.length > 0) {
            const columns = Object.keys(data[0]);
            infoHtml += `<br><strong>列数：</strong>${columns.length} 列`;

            // 显示关键列
            const keyColumns = columns.filter(col =>
                col && (col.includes('名称') || col.includes('单位') || col.includes('价格') || col.includes('本期'))
            );
            if (keyColumns.length > 0) {
                infoHtml += `<br><strong>关键列：</strong>${keyColumns.slice(0, 3).join(', ')}${keyColumns.length > 3 ? '...' : ''}`;
            }
        }

        infoElement.innerHTML = infoHtml;
        infoElement.classList.add('show');
        infoElement.style.color = '#2c3e50'; // 重置颜色
    }

    updateUploadBox(boxId, hasFile) {
        const box = document.getElementById(boxId);
        if (hasFile) {
            box.classList.add('file-selected');
        } else {
            box.classList.remove('file-selected');
        }
    }

    checkReadyToCompare() {
        const compareBtn = document.getElementById('compareBtn');
        if (this.salesData && this.basketData) {
            compareBtn.disabled = false;
        } else {
            compareBtn.disabled = true;
        }
    }

    async startComparison() {
        if (!this.salesData || !this.basketData) {
            this.showError('请先上传两个Excel文件');
            return;
        }

        try {
            this.startPerformanceMonitoring();
            this.showProgress();
            this.hideError();

            // 执行比价分析
            await this.performComparison();

            // 显示结果
            this.displayResults();

            // 记录性能和统计信息
            this.endPerformanceMonitoring('比价分析');
            const stats = this.logDataStatistics();

            this.showToast(`比价分析完成！匹配率: ${stats.matchRate}`, 'success');

        } catch (error) {
            this.showError('比价分析失败', error.message);
        } finally {
            this.hideProgress();
        }
    }

    async performComparison() {
        this.updateProgress(10, '正在分析数据结构...');
        await this.delay(300);

        // 分析销售价目表结构
        const salesStructure = this.analyzeSalesDataStructure();
        this.updateProgress(20, '销售价目表结构分析完成...');
        await this.delay(300);

        // 分析菜篮子价格表结构
        const basketStructure = this.analyzeBasketDataStructure();
        this.updateProgress(30, '菜篮子价格表结构分析完成...');
        await this.delay(300);

        // 查找最佳匹配日期
        const bestDateResult = this.findBestDateColumn(basketStructure);
        const bestDateColumn = bestDateResult.column;
        const bestDateDisplayName = bestDateResult.displayName;

        // 保存最佳日期信息供后续使用
        this.bestDateInfo = bestDateResult;

        this.updateProgress(40, `找到最佳匹配日期: ${bestDateDisplayName}...`);
        await this.delay(300);

        // 执行商品匹配
        this.updateProgress(50, '正在匹配商品名称和单位...');
        const matchedProducts = this.matchProducts(salesStructure, basketStructure, bestDateColumn);
        await this.delay(500);

        // 计算价格差异
        this.updateProgress(70, '正在计算价格差异...');
        this.comparisonResults = this.calculatePriceDifferences(matchedProducts);
        await this.delay(500);

        this.updateProgress(90, '正在生成分析报告...');
        await this.delay(300);

        this.updateProgress(100, '分析完成');
        await this.delay(300);
    }

    analyzeSalesDataStructure() {
        if (!this.salesData || this.salesData.length === 0) {
            throw new Error('销售价目表数据为空');
        }

        console.log('=== 分析销售价目表结构 ===');
        console.log('原始数据行数:', this.salesData.length);

        // 筛选菜篮子部分的数据
        const basketSalesData = this.filterBasketSalesData(this.salesData);
        console.log('菜篮子部分数据行数:', basketSalesData.length);

        if (basketSalesData.length === 0) {
            throw new Error('销售价目表中未找到菜篮子部分的数据');
        }

        // 更新销售数据为筛选后的菜篮子数据
        this.salesData = basketSalesData;

        const firstRow = this.salesData[0];
        const columns = Object.keys(firstRow);
        console.log('菜篮子部分可用列名:', columns);

        // 查找关键字段（专门针对菜篮子部分）
        const structure = {
            productNameColumn: this.findColumn(columns, ['物料名称', '商品名称', '产品名称', '名称']),
            unitColumn: this.findColumn(columns, ['计价单位', '单位', '规格']),
            priceColumn: this.findColumn(columns, ['市场价', '执行价格', '价格', '单价', '售价']), // 优先使用市场价，然后执行价格
            allColumns: columns,
            dataCount: this.salesData.length
        };

        console.log('找到的字段映射:');
        console.log('  物料名称列:', structure.productNameColumn);
        console.log('  计价单位列:', structure.unitColumn);
        console.log('  价格列:', structure.priceColumn, '(优先使用市场价，如无则使用执行价格)');

        if (!structure.productNameColumn) {
            throw new Error('销售价目表的菜篮子部分中未找到商品名称列（物料名称）');
        }
        if (!structure.unitColumn) {
            throw new Error('销售价目表的菜篮子部分中未找到计价单位列');
        }
        if (!structure.priceColumn) {
            throw new Error('销售价目表的菜篮子部分中未找到价格列（市场价或执行价格）');
        }

        return structure;
    }

    filterBasketSalesData(salesData) {
        console.log('🔍 筛选菜篮子部分的销售数据...');
        console.log('原始数据总行数:', salesData.length);
        console.log('前5行数据样本:', salesData.slice(0, 5));

        // 先检查数据结构
        if (salesData.length > 0) {
            const firstRow = salesData[0];
            const columns = Object.keys(firstRow);
            console.log('可用列名:', columns);

            // 检查是否有市场价列，如果没有则使用执行价格
            const hasMarketPrice = columns.includes('市场价');
            const hasExecutionPrice = columns.includes('执行价格');
            console.log('是否有市场价列:', hasMarketPrice);
            console.log('是否有执行价格列:', hasExecutionPrice);

            if (!hasMarketPrice && !hasExecutionPrice) {
                console.warn('⚠️ 未找到市场价或执行价格列');
            }
        }

        const basketData = [];
        let foundBasketSection = false;
        let basketSectionEnded = false;

        // 严格按照Excel文件中的分类标识
        const basketSectionStart = '菜篮子部分';

        // 可能的分类结束标识（包含"部分"后缀的分类）
        const sectionEndKeywords = [
            '市场部分', '蔬菜部分', '肉类部分', '水产部分', '粮油部分', '调料部分',
            '其他部分', '禽蛋部分', '奶制品部分', '豆制品部分', '面食类部分',
            '熟食类部分', '水果部分', '蔬菜', '肉类', '水产', '粮油', '调料',
            '其他', '禽蛋', '奶制品', '豆制品', '面食类', '熟食类', '水果', '市场'
        ];

        for (let i = 0; i < salesData.length; i++) {
            const row = salesData[i];

            // 检查是否找到"菜篮子部分"标识
            const rowText = Object.values(row).join(' ');

            // 如果找到"菜篮子部分"标识行
            if (rowText.includes(basketSectionStart) && !foundBasketSection) {
                foundBasketSection = true;
                basketSectionEnded = false;
                console.log(`✅ 找到菜篮子部分起始行 ${i+1}:`, row);
                continue; // 跳过标题行
            }

            // 如果已经找到菜篮子部分，检查是否遇到新的分类标识
            if (foundBasketSection && !basketSectionEnded) {
                const trimmedRowText = rowText.trim();

                // 检查是否遇到其他分类标识（特别是"蔬菜"等）
                const isNewSection = sectionEndKeywords.some(keyword => {
                    return trimmedRowText === keyword ||
                           trimmedRowText.includes(keyword + '部分') ||
                           (trimmedRowText === keyword && keyword !== basketSectionStart);
                });

                if (isNewSection) {
                    basketSectionEnded = true;
                    console.log(`❌ 遇到新分类，菜篮子部分结束于行 ${i+1}: "${trimmedRowText}"`);
                    break;
                }
            }

            // 检查是否是有效的数据行（包含物料名称、计价单位、价格）
            const hasProductName = row['物料名称'] && row['物料名称'].toString().trim() !== '';
            const hasUnit = row['计价单位'] && row['计价单位'].toString().trim() !== '';

            // 优先使用市场价，如果没有则使用执行价格
            const marketPrice = row['市场价'];
            const executionPrice = row['执行价格'];
            const hasPrice = (marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '') ||
                           (executionPrice !== undefined && executionPrice !== null && executionPrice.toString().trim() !== '');
            const priceValue = marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '' ? marketPrice : executionPrice;
            const priceField = marketPrice !== undefined && marketPrice !== null && marketPrice.toString().trim() !== '' ? '市场价' : '执行价格';

            // 如果在菜篮子部分且未结束，收集所有有效数据行
            if (foundBasketSection && !basketSectionEnded && hasProductName && hasUnit && hasPrice) {
                basketData.push(row);
                console.log(`✅ 收集菜篮子数据行 ${i+1}:`, {
                    物料名称: row['物料名称'],
                    计价单位: row['计价单位'],
                    [priceField]: priceValue
                });
            }


        }

        console.log(`✅ 筛选完成，找到 ${basketData.length} 行菜篮子数据`);

        // 如果仍然没有找到数据，输出更详细的调试信息
        if (basketData.length === 0) {
            console.log('❌ 未找到菜篮子数据，进行详细分析...');
            console.log('检查前10行数据的物料名称:');
            for (let i = 0; i < Math.min(10, salesData.length); i++) {
                const row = salesData[i];
                console.log(`行${i+1}: 物料名称="${row['物料名称']}", 计价单位="${row['计价单位']}", 市场价="${row['市场价']}"`);
            }
        }

        return basketData;
    }



    analyzeBasketDataStructure() {
        if (!this.basketData || this.basketData.length === 0) {
            throw new Error('菜篮子价格表数据为空');
        }

        const firstRow = this.basketData[0];
        const columns = Object.keys(firstRow);

        // 输出调试信息
        console.log('=== 菜篮子数据结构详细分析 ===');
        console.log('菜篮子价格表列名:', columns);
        console.log('菜篮子价格表前几行数据:', this.basketData.slice(0, 3));
        console.log('第一行数据类型:', Array.isArray(firstRow) ? 'Array' : 'Object');
        console.log('第一行数据内容:', firstRow);
        if (!Array.isArray(firstRow)) {
            console.log('对象的所有键值对:');
            Object.entries(firstRow).forEach(([key, value], index) => {
                console.log(`  [${index}] ${key}: ${value}`);
            });
        }
        console.log('=== 详细分析结束 ===');

        // 对于菜篮子价格表，第一列通常是商品名称列，不管列名是什么
        let productNameColumn = null;

        // 先尝试按关键词查找
        productNameColumn = this.findColumn(columns, ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']);

        // 如果没找到，使用第一列作为商品名称列
        if (!productNameColumn && columns.length > 0) {
            productNameColumn = columns[0];
            console.log('使用第一列作为商品名称列:', productNameColumn);
        }

        // 查找关键字段
        const unitColumnName = this.findColumn(columns, ['计价单位', '单位', '规格', '计量单位']);

        const structure = {
            productNameColumn: productNameColumn,
            unitColumn: unitColumnName,
            allColumns: columns,
            dataCount: this.basketData.length,
            dateColumns: this.findDateColumns(columns)
        };

        console.log('找到的商品名称列:', structure.productNameColumn);
        console.log('找到的单位列:', structure.unitColumn);
        console.log('找到的日期列:', structure.dateColumns);

        if (structure.productNameColumn === null || structure.productNameColumn === undefined) {
            throw new Error(`菜篮子价格表中未找到商品名称列。可用列名: ${columns.join(', ')}`);
        }
        if (structure.unitColumn === null || structure.unitColumn === undefined) {
            console.warn('未找到计价单位列，将跳过单位匹配');
        }
        if (structure.dateColumns.length === 0) {
            throw new Error(`菜篮子价格表中未找到日期相关的价格列。可用列名: ${columns.join(', ')}`);
        }

        return structure;
    }

    findColumn(columns, possibleNames) {
        for (const name of possibleNames) {
            const found = columns.find(col => col && col.includes(name));
            if (found) return found;
        }
        return null;
    }

    findDateColumns(columns) {
        const dateColumns = [];
        const datePattern = /(\d{1,2})[日号]/;
        const monthPattern = /(\d{1,2})月(\d{1,2})[日号]/;
        const fullDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;
        // 新增：组合格式的日期列匹配，如 "2025年06月26日-本期"
        const combinedDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日-(.+)/;

        console.log('=== 查找日期列 ===');
        console.log('所有列名:', columns);
        console.log('列名数量:', columns.length);
        console.log('前10个列名:', columns.slice(0, 10));

        // 特别检查是否有"本期"列
        const currentPeriodColumns = columns.filter(col => col && col.includes('本期'));
        console.log('🎯 包含"本期"的列:', currentPeriodColumns);

        // 首先查找所有日期相关的列，并建立日期到列索引的映射
        const dateToColumns = new Map(); // 存储日期到相关列的映射

        for (let i = 0; i < columns.length; i++) {
            const col = columns[i];
            if (!col) continue;

            console.log(`检查列 [${i}]: "${col}"`);

            // 优先查找组合格式的日期列（如 "2025年06月26日-本期"）
            if (combinedDatePattern.test(col)) {
                const match = col.match(combinedDatePattern);
                if (match) {
                    const day = parseInt(match[3]);
                    const priceType = match[4];
                    console.log(`找到组合日期列: ${col} (日期: ${day}, 类型: ${priceType})`);

                    // 优先选择"本期"价格
                    const priority = priceType.includes('本期') ?
                        this.getDatePriority(day) - 100 : // 本期优先级更高
                        this.getDatePriority(day);

                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: day,
                        year: parseInt(match[1]),
                        month: parseInt(match[2]),
                        priceType: priceType,
                        priority: priority
                    });
                }
            }
            // 查找单独的"本期"列，需要根据其他日期列推断最佳日期
            else if (col && col.trim() === '本期') {
                console.log(`找到单独的"本期"列: ${col} (索引: ${i})`);

                // 从所有其他日期列中找到最佳日期（按照25日优先规则）
                console.log(`🔍 从其他列推断本期日期...`);
                const inferredDate = this.inferCurrentPeriodFromOtherColumns(columns);
                console.log(`✅ 推断的本期日期:`, inferredDate);

                dateColumns.push({
                    column: col, // 使用原始列名"本期"
                    columnIndex: i,
                    day: inferredDate.day,
                    year: inferredDate.year,
                    month: inferredDate.month,
                    priceType: '本期',
                    priority: -1000, // 本期数据最高优先级
                    isCurrentPeriod: true,
                    displayName: `${inferredDate.month}月${inferredDate.day}日本期`
                });
            }
            // 查找其他包含"本期"的列
            else if (col && col.includes('本期')) {
                console.log(`找到包含"本期"的列: ${col} (索引: ${i})`);
                dateColumns.push({
                    column: col,
                    columnIndex: i,
                    day: 26, // 默认最新日期
                    priceType: '本期',
                    priority: this.getDatePriority(26) - 150 // 高优先级
                });
            }
            // 查找包含"本期"的日期列
            else if (datePattern.test(col) && col.includes('本期')) {
                const match = col.match(datePattern);
                if (match) {
                    console.log(`找到"本期"日期列: ${col}`);
                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: parseInt(match[1]),
                        priority: this.getDatePriority(parseInt(match[1])) - 50 // 本期优先级高
                    });
                }
            }
            // 查找完整日期格式 YYYY年MM月DD日
            else if (fullDatePattern.test(col)) {
                const match = col.match(fullDatePattern);
                if (match) {
                    const day = parseInt(match[3]);
                    console.log(`找到完整日期列: ${col} (日期: ${day})`);
                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: day,
                        year: parseInt(match[1]),
                        month: parseInt(match[2]),
                        priority: this.getDatePriority(day)
                    });
                }
            }
            // 查找月日格式的列
            else if (monthPattern.test(col)) {
                const match = col.match(monthPattern);
                if (match) {
                    console.log(`找到月日格式列: ${col}`);
                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: parseInt(match[2]),
                        month: parseInt(match[1]),
                        priority: this.getDatePriority(parseInt(match[2]))
                    });
                }
            }
            // 查找"本期"列（最高优先级）
            else if (col.includes('本期')) {
                console.log(`找到本期列: ${col}`);
                // 从列名中提取日期信息，如"6月26日本期"
                const dateInfo = this.extractDateFromColumnName(col);
                if (dateInfo) {
                    console.log(`从列名提取日期: ${dateInfo.month}月${dateInfo.day}日`);
                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: dateInfo.day,
                        month: dateInfo.month,
                        year: dateInfo.year || 2025,
                        priority: this.getDatePriority(dateInfo.day), // 按照25日优先的规则
                        isCurrentPeriod: true,
                        displayName: `${dateInfo.month}月${dateInfo.day}日本期`
                    });
                } else {
                    // 如果无法从列名提取日期，从其他列推断本期日期
                    console.log(`无法从列名提取日期，从其他列推断本期日期: ${col}`);
                    const inferredDate = this.inferCurrentPeriodFromOtherColumns(columns);
                    console.log(`推断的本期日期:`, inferredDate);

                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: inferredDate.day,
                        month: inferredDate.month,
                        year: inferredDate.year,
                        priority: -1, // 本期数据仍然有高优先级
                        isCurrentPeriod: true,
                        displayName: `${inferredDate.month}月${inferredDate.day}日本期`
                    });
                }
            }
            // 查找其他可能的价格列
            else if (col.includes('价格') || col.includes('单价') || col.includes('元') || /\d+[日号]/.test(col)) {
                const match = col.match(datePattern);
                const day = match ? parseInt(match[1]) : 25; // 默认25日
                console.log(`找到价格相关列: ${col}`);
                dateColumns.push({
                    column: col,
                    columnIndex: i,
                    day: day,
                    priority: this.getDatePriority(day)
                });
            }
        }

        // 按优先级排序
        dateColumns.sort((a, b) => a.priority - b.priority);

        console.log('找到的日期列数量:', dateColumns.length);
        console.log('日期列详情:');
        dateColumns.forEach((col, index) => {
            console.log(`  ${index + 1}. ${col.column} (优先级: ${col.priority}, 索引: ${col.columnIndex})`);
        });

        // 显示选择的最佳列
        if (dateColumns.length > 0) {
            console.log(`选择的最佳价格列: ${dateColumns[0].column} (索引: ${dateColumns[0].columnIndex})`);

            // 简化调试：检查这个价格列的前几个值
            console.log('=== 价格列数据检查 ===');
            const priceColumnName = dateColumns[0].column;
            this.basketData.slice(0, 3).forEach((item, index) => {
                const priceValue = item[priceColumnName];
                const parsedPrice = this.parsePrice(priceValue);
                console.log(`${index + 1}. ${item[this.findColumn(Object.keys(item), ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']) || Object.keys(item)[0]]}: 原值="${priceValue}" -> 解析="${parsedPrice}"`);
            });
            console.log('=== 价格列检查完成 ===');
        }

        console.log('=== 日期列查找完成 ===');
        return dateColumns;
    }

    getDatePriority(day) {
        // 严格按照README.md中的日期匹配策略：25日→26日→27日→28日→29日...
        const priorityOrder = [25, 26, 27, 28, 29];

        const index = priorityOrder.indexOf(day);
        if (index !== -1) {
            return index; // 返回在优先级数组中的位置，越小优先级越高
        }

        // 如果日期不在预定义列表中，给予较低优先级
        // 按照距离25日的远近来排序，但优先级低于前5个日期
        const distance = Math.abs(day - 25);
        return 10 + distance; // 确保优先级低于前5个日期
    }

    extractDateFromColumnName(columnName) {
        console.log(`尝试从列名提取日期: "${columnName}"`);

        // 匹配各种日期格式：
        // "6月26日本期", "06月26日本期", "2025年6月26日本期", "6月26日-本期" 等
        const patterns = [
            // 完整年月日格式：2025年6月26日本期
            /(\d{4})年(\d{1,2})月(\d{1,2})日.*本期/,
            // 月日格式：6月26日本期
            /(\d{1,2})月(\d{1,2})日.*本期/,
            // 带分隔符：6月26日-本期
            /(\d{1,2})月(\d{1,2})日[-_].*本期/,
            // 数字格式：06-26本期
            /(\d{1,2})-(\d{1,2}).*本期/,
            // 数字格式：0626本期
            /(\d{2})(\d{2}).*本期/
        ];

        for (let i = 0; i < patterns.length; i++) {
            const match = columnName.match(patterns[i]);
            if (match) {
                let year, month, day;

                if (i === 0) {
                    // 完整年月日格式
                    year = parseInt(match[1]);
                    month = parseInt(match[2]);
                    day = parseInt(match[3]);
                } else if (i === 1 || i === 2) {
                    // 月日格式
                    year = 2025; // 默认年份
                    month = parseInt(match[1]);
                    day = parseInt(match[2]);
                } else if (i === 3) {
                    // 数字格式 MM-DD
                    year = 2025;
                    month = parseInt(match[1]);
                    day = parseInt(match[2]);
                } else if (i === 4) {
                    // 数字格式 MMDD
                    year = 2025;
                    month = parseInt(match[1]);
                    day = parseInt(match[2]);
                }

                console.log(`✅ 成功提取日期: ${year}年${month}月${day}日 (模式${i+1})`);
                return { year, month, day };
            }
        }

        console.log(`❌ 无法从列名提取日期: "${columnName}"`);
        return null;
    }

    inferCurrentPeriodFromOtherColumns(columns) {
        console.log(`🔍 从其他列推断本期日期...`);
        console.log(`可用列名:`, columns);

        // 查找所有包含日期的列
        const fullDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;
        const monthDayPattern = /(\d{1,2})月(\d{1,2})日/;

        const foundDates = [];

        for (const col of columns) {
            if (!col) continue;

            // 匹配完整日期格式
            let match = col.match(fullDatePattern);
            if (match) {
                const year = parseInt(match[1]);
                const month = parseInt(match[2]);
                const day = parseInt(match[3]);
                foundDates.push({ year, month, day, column: col });
                console.log(`📅 找到完整日期: ${year}年${month}月${day}日 (${col})`);
                continue;
            }

            // 匹配月日格式
            match = col.match(monthDayPattern);
            if (match) {
                const year = 2025; // 默认年份
                const month = parseInt(match[1]);
                const day = parseInt(match[2]);
                foundDates.push({ year, month, day, column: col });
                console.log(`📅 找到月日格式: ${year}年${month}月${day}日 (${col})`);
            }
        }

        if (foundDates.length > 0) {
            // 按照README.md的规则：25日优先，然后26、24、27、23...
            foundDates.sort((a, b) => this.getDatePriority(a.day) - this.getDatePriority(b.day));
            const bestDate = foundDates[0];
            console.log(`✅ 根据优先级规则选择: ${bestDate.year}年${bestDate.month}月${bestDate.day}日 (来自: ${bestDate.column})`);
            return bestDate;
        }

        // 如果没有找到任何日期，使用默认的25日（最高优先级）
        console.log(`❌ 未找到任何日期列，使用默认: 2025年6月25日`);
        return { year: 2025, month: 6, day: 25 };
    }

    inferCurrentPeriodDate() {
        // 从菜篮子文件名中推断当前期日期
        if (this.basketFileName) {
            console.log('从文件名推断本期日期:', this.basketFileName);

            // 匹配文件名中的日期范围，如：2025年06月01日_2025年06月26日 或 2025年06月01日-2025年06月26日
            const dateRangeMatch = this.basketFileName.match(/(\d{4})年(\d{1,2})月(\d{1,2})日[_-](\d{4})年(\d{1,2})月(\d{1,2})日/);
            if (dateRangeMatch) {
                // 使用结束日期作为本期日期
                const year = parseInt(dateRangeMatch[4]);
                const month = parseInt(dateRangeMatch[5]);
                const day = parseInt(dateRangeMatch[6]);
                console.log(`✅ 从日期范围推断本期日期: ${year}年${month}月${day}日`);
                return { year, month, day };
            }

            // 匹配单个日期，如：2025年06月26日 或 2025年6月26日
            const singleDateMatch = this.basketFileName.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/);
            if (singleDateMatch) {
                const year = parseInt(singleDateMatch[1]);
                const month = parseInt(singleDateMatch[2]);
                const day = parseInt(singleDateMatch[3]);
                console.log(`✅ 从单个日期推断本期日期: ${year}年${month}月${day}日`);
                return { year, month, day };
            }

            console.log('❌ 无法从文件名中匹配到日期格式');
        } else {
            console.log('❌ 没有菜篮子文件名');
        }

        // 从其他日期列中找到最新的日期作为本期
        if (this.basketData && this.basketData.length > 0) {
            const headers = Object.keys(this.basketData[0]);
            const datePattern = /(\d{4})年(\d{2})月(\d{2})日/;
            let latestDate = { year: 2025, month: 6, day: 25 }; // 默认值

            for (const header of headers) {
                const match = header.match(datePattern);
                if (match) {
                    const year = parseInt(match[1]);
                    const month = parseInt(match[2]);
                    const day = parseInt(match[3]);

                    // 比较日期，找到最新的
                    const currentDateValue = year * 10000 + month * 100 + day;
                    const latestDateValue = latestDate.year * 10000 + latestDate.month * 100 + latestDate.day;

                    if (currentDateValue > latestDateValue) {
                        latestDate = { year, month, day };
                    }
                }
            }

            console.log(`从列名推断本期日期: ${latestDate.year}年${latestDate.month}月${latestDate.day}日`);
            return latestDate;
        }

        // 默认返回当前日期
        const now = new Date();
        return {
            year: now.getFullYear(),
            month: now.getMonth() + 1,
            day: now.getDate()
        };
    }

    findBestDateColumn(basketStructure) {
        console.log('=== 查找最佳日期列 ===');
        console.log('可用日期列:', basketStructure.dateColumns);

        if (basketStructure.dateColumns.length === 0) {
            throw new Error('未找到可用的日期价格列');
        }

        // 详细显示所有日期列的优先级
        console.log('=== 日期列优先级详情 ===');
        basketStructure.dateColumns.forEach((col, index) => {
            console.log(`${index + 1}. 列名: "${col.column}"`);
            if (col.isCurrentPeriod && col.displayName) {
                console.log(`   显示名称: ${col.displayName}`);
            }
            console.log(`   日期: ${col.day}日`);
            console.log(`   优先级: ${col.priority}`);
            console.log(`   列索引: ${col.columnIndex}`);
            if (col.isCurrentPeriod) {
                console.log(`   🎯 本期数据 (最高优先级)`);
            }
            console.log('---');
        });

        const bestDate = basketStructure.dateColumns[0];
        console.log('🎯 最终选择的日期列:');
        console.log(`   列名: "${bestDate.column}"`);
        if (bestDate.displayName) {
            console.log(`   显示名称: ${bestDate.displayName}`);
        }
        console.log(`   日期: ${bestDate.day}日`);
        console.log(`   优先级: ${bestDate.priority}`);
        console.log(`   列索引: ${bestDate.columnIndex}`);

        // 检查这个列在实际数据中的值
        if (this.basketData && this.basketData.length > 0) {
            console.log('该列在前3行数据中的值:');
            for (let i = 0; i < Math.min(3, this.basketData.length); i++) {
                const value = this.basketData[i][bestDate.column];
                console.log(`  行${i+1}: "${value}" (类型: ${typeof value})`);
            }
        }

        // 返回显示名称或列名
        return {
            column: bestDate.column,
            displayName: bestDate.displayName || bestDate.column,
            dateInfo: bestDate
        };
    }

    matchProducts(salesStructure, basketStructure, bestDateColumn) {
        const matchedProducts = [];

        console.log('开始匹配商品...');
        console.log('销售数据结构:', salesStructure);
        console.log('菜篮子数据结构:', basketStructure);
        console.log('最佳日期列:', bestDateColumn);

        // 打印前几个销售数据样本
        console.log('销售数据样本:', this.salesData.slice(0, 3));
        console.log('菜篮子数据样本:', this.basketData.slice(0, 3));

        for (const salesItem of this.salesData) {
            const productName = salesItem[salesStructure.productNameColumn];
            const unit = salesItem[salesStructure.unitColumn];
            const salesPrice = this.parsePrice(salesItem[salesStructure.priceColumn]);

            console.log(`处理销售商品: ${productName}, 单位: ${unit}, 价格: ${salesPrice}`);

            if (!productName || !unit || salesPrice === null) {
                console.log('跳过无效数据:', { productName, unit, salesPrice });
                continue; // 跳过无效数据
            }

            // 在菜篮子数据中查找匹配的商品
            const basketMatch = this.findBasketMatch(
                productName,
                unit,
                basketStructure,
                bestDateColumn
            );

            console.log(`商品 ${productName} 匹配结果:`, basketMatch);

            const basketPrice = basketMatch ? basketMatch.price : null;
            console.log(`最终菜篮子价格: ${basketPrice} (类型: ${typeof basketPrice})`);

            // 特殊调试：如果是菜心且没有匹配到价格，输出详细信息
            if (productName === '菜心' && basketPrice === null) {
                console.log('🔍 菜心匹配失败详细分析:');
                console.log('  销售数据:', { productName, unit, salesPrice });
                console.log('  菜篮子结构:', basketStructure);
                console.log('  价格列:', bestDateColumn);
                console.log('  菜篮子前3条数据:');
                this.basketData.slice(0, 3).forEach((item, i) => {
                    console.log(`    ${i+1}. ${item[basketStructure.productNameColumn]} | ${item[basketStructure.unitColumn]} | ${item[bestDateColumn]}`);
                });
            }

            matchedProducts.push({
                productName: productName,
                unit: unit,
                salesPrice: salesPrice,
                basketPrice: basketPrice,
                basketMatch: basketMatch,
                matchStatus: basketMatch ? 'matched' : 'not_found',
                matchType: basketMatch ? basketMatch.matchType : null
            });
        }

        return matchedProducts;
    }

    // 检查单位是否兼容 - 从调试工具移植的成功逻辑
    isUnitCompatible(salesUnit, basketUnit) {
        // 直接匹配
        if (salesUnit === basketUnit) {
            return true;
        }

        // 单位转换映射
        const unitMappings = {
            '斤': ['元/500克', '500克', '元/斤'],
            '元/500克': ['斤', '500克', '元/斤'],
            '500克': ['斤', '元/500克', '元/斤'],
            '元/斤': ['斤', '元/500克', '500克'],
            '公斤': ['元/公斤', 'kg', '千克'],
            '元/公斤': ['公斤', 'kg', '千克'],
            'kg': ['公斤', '元/公斤', '千克'],
            '千克': ['公斤', '元/公斤', 'kg'],
            '桶': ['元/桶'],
            '元/桶': ['桶'],
            '个': ['元/个'],
            '元/个': ['个']
        };

        // 检查是否有映射关系
        if (unitMappings[salesUnit] && unitMappings[salesUnit].includes(basketUnit)) {
            return true;
        }

        if (unitMappings[basketUnit] && unitMappings[basketUnit].includes(salesUnit)) {
            return true;
        }

        return false;
    }

    findBasketMatch(productName, unit, basketStructure, priceColumn) {
        console.log(`=== 查找匹配: ${productName} (${unit}) ===`);
        console.log('菜篮子数据结构:', basketStructure);
        console.log('价格列:', priceColumn);

        // 打印前几个菜篮子数据样本用于调试
        if (this.basketData.length > 0) {
            console.log('菜篮子数据样本:');
            this.basketData.slice(0, 5).forEach((item, index) => {
                const productName = item[basketStructure.productNameColumn];
                const unit = item[basketStructure.unitColumn];
                const priceValue = item[priceColumn];
                console.log(`  [${index}] 商品: "${productName}", 单位: "${unit}", 价格原值: "${priceValue}" (类型: ${typeof priceValue})`);

                // 测试价格解析
                const parsedPrice = this.parsePrice(priceValue);
                console.log(`    -> 解析后价格: ${parsedPrice}`);
            });
        }

        // 检查价格列是否有效
        if (priceColumn === null || priceColumn === undefined) {
            console.log('价格列无效，无法进行匹配');
            return null;
        }

        console.log('开始匹配策略...');

        // 策略1: 精确匹配（使用单位兼容性检查）
        let exactMatch = this.basketData.find(item => {
            const basketProductName = item[basketStructure.productNameColumn];
            const basketUnit = item[basketStructure.unitColumn];
            const isMatch = basketProductName === productName && this.isUnitCompatible(unit, basketUnit);
            if (isMatch) {
                console.log(`✓ 精确匹配成功: ${basketProductName} (${basketUnit}) - 单位兼容: ${unit} ↔ ${basketUnit}`);
            }
            return isMatch;
        });

        if (exactMatch) {
            const priceValue = exactMatch[priceColumn];
            console.log(`精确匹配价格值: "${priceValue}" (类型: ${typeof priceValue})`);
            const price = this.parsePrice(priceValue);
            console.log(`精确匹配解析后价格: ${price}`);
            if (price !== null) {
                return { item: exactMatch, price: price, matchType: 'exact' };
            }
        }

        // 策略2: 商品名称包含匹配（忽略编号）
        const cleanProductName = this.cleanProductName(productName);
        console.log(`清理后的商品名称: "${cleanProductName}"`);
        
        let containsMatch = this.basketData.find(item => {
            const basketProductName = item[basketStructure.productNameColumn];
            const basketUnit = item[basketStructure.unitColumn];
            
            if (!basketProductName || !basketUnit) return false;
            
            const cleanBasketName = this.cleanProductName(basketProductName);
            console.log(`比较包含匹配: "${cleanBasketName}" 是否包含 "${cleanProductName}"`);
            
            // 检查菜篮子商品名称是否包含销售商品名称
            const nameContains = cleanBasketName.includes(cleanProductName) || cleanProductName.includes(cleanBasketName);
            const unitMatch = this.isUnitCompatible(unit, basketUnit);
            
            if (nameContains) {
                console.log(`  ✓ 名称包含匹配: "${basketProductName}" <-> "${productName}"`);
                console.log(`  单位匹配: "${basketUnit}" vs "${unit}" = ${unitMatch}`);
            }
            
            return nameContains && unitMatch;
        });

        if (containsMatch) {
            const priceValue = containsMatch[priceColumn];
            console.log(`包含匹配价格值: "${priceValue}"`);
            const price = this.parsePrice(priceValue);
            console.log(`包含匹配解析后价格: ${price}`);
            if (price !== null) {
                return { item: containsMatch, price: price, matchType: 'contains' };
            }
        }

        // 策略3: 蔬菜类别匹配
        const vegetableMatch = this.findVegetableCategoryMatch(productName, unit, priceColumn, basketStructure);
        if (vegetableMatch) {
            console.log(`✓ 找到蔬菜类别匹配`);
            return vegetableMatch;
        }

        // 策略4: 模糊匹配
        const fuzzyMatch = this.basketData.find(item => {
            const basketProductName = item[basketStructure.productNameColumn];
            const basketUnit = item[basketStructure.unitColumn];
            const isSimilar = this.isSimilarProductName(productName, basketProductName);
            const unitMatch = this.isUnitCompatible(unit, basketUnit);
            const isMatch = isSimilar && unitMatch;

            if (isSimilar) {
                console.log(`模糊匹配候选: ${basketProductName} (${basketUnit}) 相似度高`);
            }

            return isMatch;
        });

        if (fuzzyMatch) {
            const priceValue = fuzzyMatch[priceColumn];
            console.log(`模糊匹配价格值: "${priceValue}"`);
            const price = this.parsePrice(priceValue);
            console.log(`模糊匹配解析后价格: ${price}`);
            if (price !== null) {
                return { item: fuzzyMatch, price: price, matchType: 'fuzzy' };
            }
        }

        console.log(`✗ 未找到任何匹配: ${productName} (${unit})`);
        return null;
    }

    // 新增：蔬菜类别匹配方法
    findVegetableCategoryMatch(productName, unit, priceColumn, basketStructure) {
        // 常见蔬菜分类映射
        const vegetableCategories = {
            '叶菜类': ['菜心', '上海青', '大白菜', '奶白菜', '花菜', '生菜', '菠菜', '韭菜', '芹菜', '茼蒿'],
            '茄果类': ['番茄', '茄子', '辣椒', '青椒', '彩椒'],
            '瓜类': ['冬瓜', '南瓜', '丝瓜', '苦瓜', '黄瓜'],
            '豆类': ['豆角', '四季豆', '扁豆', '豌豆'],
            '根茎类': ['萝卜', '胡萝卜', '土豆', '红薯', '山药', '莲藕'],
            '葱蒜类': ['大葱', '小葱', '韭黄', '蒜苗', '大蒜', '生姜']
        };

        console.log(`查找蔬菜类别匹配: ${productName}`);

        for (const [category, vegetables] of Object.entries(vegetableCategories)) {
            if (vegetables.some(veg => productName.includes(veg) || veg.includes(productName))) {
                console.log(`商品 ${productName} 属于类别: ${category}`);
                
                // 在菜篮子数据中查找该类别
                const categoryMatch = this.basketData.find(item => {
                    const basketProductName = item[basketStructure.productNameColumn];
                    const basketUnit = item[basketStructure.unitColumn];
                    
                    if (!basketProductName) return false;
                    
                    const nameMatch = basketProductName.includes(category) || 
                                    basketProductName.includes('蔬菜') ||
                                    basketProductName.includes('菜类');
                    const unitMatch = basketUnit === unit || this.isUnitMatch(basketUnit, unit);
                    
                    console.log(`检查类别匹配: "${basketProductName}" 包含 "${category}"? ${nameMatch}, 单位匹配? ${unitMatch}`);
                    
                    return nameMatch && unitMatch;
                });

                if (categoryMatch) {
                    const priceValue = categoryMatch[priceColumn];
                    const price = this.parsePrice(priceValue);
                    console.log(`类别匹配找到: ${categoryMatch[basketStructure.productNameColumn]}, 价格: ${price}`);
                    if (price !== null) {
                        return { 
                            item: categoryMatch, 
                            price: price, 
                            matchType: 'category',
                            category: category 
                        };
                    }
                }
            }
        }

        return null;
    }

    // 新增：单位匹配检查
    isUnitMatch(unit1, unit2) {
        if (!unit1 || !unit2) return false;

        // 标准化单位名称
        const normalizeUnit = (unit) => {
            return unit.replace(/[（）()]/g, '').trim().toLowerCase();
        };

        const normalized1 = normalizeUnit(unit1);
        const normalized2 = normalizeUnit(unit2);

        // 直接匹配
        if (normalized1 === normalized2) return true;

        // 重量单位换算匹配（关键修复）
        console.log(`单位匹配调试: "${normalized1}" vs "${normalized2}"`);

        const weightEquivalents = [
            ['斤', '元/500克', '500克', '500g'],
            ['公斤', '元/1000克', '1000克', '1000g', 'kg', '千克'],
            ['克', 'g'],
            ['两', '元/50克', '50克', '50g']
        ];

        for (const equivalentGroup of weightEquivalents) {
            console.log(`检查等价组: [${equivalentGroup.join(', ')}]`);

            const unit1InGroup = equivalentGroup.some(equiv => {
                const match = normalized1 === equiv || normalized1.includes(equiv) || equiv.includes(normalized1);
                if (match) console.log(`  单位1 "${normalized1}" 匹配 "${equiv}"`);
                return match;
            });

            const unit2InGroup = equivalentGroup.some(equiv => {
                const match = normalized2 === equiv || normalized2.includes(equiv) || equiv.includes(normalized2);
                if (match) console.log(`  单位2 "${normalized2}" 匹配 "${equiv}"`);
                return match;
            });

            console.log(`  单位1在组内: ${unit1InGroup}, 单位2在组内: ${unit2InGroup}`);

            if (unit1InGroup && unit2InGroup) {
                console.log(`✓ 单位换算匹配成功: "${unit1}" <-> "${unit2}"`);
                return true;
            }
        }

        // 常见单位别名匹配
        const unitAliases = {
            '斤': ['斤', '市斤'],
            '公斤': ['公斤', 'kg', '千克'],
            '克': ['克', 'g'],
            '个': ['个', '只', '头'],
            '把': ['把', '束'],
            '袋': ['袋', '包']
        };

        for (const [standard, aliases] of Object.entries(unitAliases)) {
            if (aliases.includes(normalized1) && aliases.includes(normalized2)) {
                return true;
            }
        }

        return false;
    }

    isSimilarProductName(name1, name2) {
        if (!name1 || !name2) return false;

        // 移除常见的修饰词和空格
        const clean1 = this.cleanProductName(name1);
        const clean2 = this.cleanProductName(name2);

        // 检查是否包含相同的核心词汇
        return clean1.includes(clean2) || clean2.includes(clean1) ||
               this.calculateSimilarity(clean1, clean2) > 0.7;
    }

    cleanProductName(name) {
        return name.replace(/[（）()【】\[\]]/g, '')
                  .replace(/\s+/g, '')
                  .toLowerCase();
    }

    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;

        if (longer.length === 0) return 1.0;

        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    parsePrice(priceStr) {
        console.log(`parsePrice 输入: "${priceStr}" (类型: ${typeof priceStr})`);

        if (priceStr === null || priceStr === undefined || priceStr === '') {
            console.log(`parsePrice 返回 null: 输入为空`);
            return null;
        }

        // 如果已经是数字，直接返回
        if (typeof priceStr === 'number') {
            console.log(`parsePrice 输入已是数字: ${priceStr}`);
            return isNaN(priceStr) ? null : priceStr;
        }

        // 转换为字符串
        let priceString = String(priceStr).trim();
        console.log(`parsePrice 字符串化: "${priceString}"`);

        // 处理特殊情况：如果包含"无"、"缺"、"-"等表示无价格的字符
        if (/^(无|缺|--|—|\/|\\|N\/A|n\/a|NULL|null)$/.test(priceString)) {
            console.log(`parsePrice 识别为无价格标识: "${priceString}"`);
            return null;
        }

        // 移除货币符号、单位等非数字字符，但保留数字、小数点和负号
        const cleanPrice = priceString.replace(/[^\d.-]/g, '');
        console.log(`parsePrice 清理后: "${cleanPrice}"`);

        // 如果清理后为空，返回null
        if (!cleanPrice) {
            console.log(`parsePrice 清理后为空`);
            return null;
        }

        const price = parseFloat(cleanPrice);
        console.log(`parsePrice 解析结果: ${price} (isNaN: ${isNaN(price)})`);

        return isNaN(price) ? null : price;
    }

    calculatePriceDifferences(matchedProducts) {
        const results = [];
        let totalMatched = 0;
        let totalPriceDiff = 0;

        console.log('=== 开始计算价格差异 ===');
        console.log('匹配的商品数量:', matchedProducts.length);

        for (const product of matchedProducts) {
            let priceDiff = null;
            let priceDiffPercent = null;
            let status = product.matchStatus;

            console.log(`处理商品: ${product.productName}`);
            console.log(`  销售价格: ${product.salesPrice} (类型: ${typeof product.salesPrice})`);
            console.log(`  菜篮子价格: ${product.basketPrice} (类型: ${typeof product.basketPrice})`);
            console.log(`  匹配状态: ${product.matchStatus}`);

            if (product.basketPrice !== null && product.salesPrice !== null) {
                // 修改计算公式：（销售价格-菜篮子价格）/菜篮子价格*100%
                priceDiff = product.salesPrice - product.basketPrice;
                priceDiffPercent = product.basketPrice !== 0 ?
                    (priceDiff / product.basketPrice * 100) : 0;
                totalMatched++;
                totalPriceDiff += Math.abs(priceDiffPercent);

                console.log(`  计算结果: 差异=${priceDiff}, 百分比=${priceDiffPercent}%`);
            } else {
                console.log(`  跳过计算: 销售价格或菜篮子价格为空`);
            }

            results.push({
                productName: product.productName,
                unit: product.unit,
                salesPrice: product.salesPrice,
                basketPrice: product.basketPrice,
                priceDiff: priceDiff,
                priceDiffPercent: priceDiffPercent,
                status: status,
                matchType: product.matchType || (product.basketMatch ? product.basketMatch.matchType : null)
            });
        }

        console.log('=== 价格差异计算完成 ===');
        console.log('成功匹配的商品数量:', totalMatched);
        console.log('结果样本:', results.slice(0, 3));

        // 保存统计信息
        this.statistics = {
            totalProducts: matchedProducts.length,
            totalMatched: totalMatched,
            avgPriceDiff: totalMatched > 0 ? (totalPriceDiff / totalMatched).toFixed(2) : 0,
            matchedDate: this.getMatchedDateFromColumn()
        };

        return results;
    }

    getMatchedDateFromColumn() {
        console.log('=== 获取匹配日期字符串 ===');
        console.log('bestDateInfo:', this.bestDateInfo);

        // 使用保存的最佳日期信息
        if (this.bestDateInfo && this.bestDateInfo.displayName) {
            console.log('使用bestDateInfo.displayName:', this.bestDateInfo.displayName);
            return this.bestDateInfo.displayName;
        }

        // 备用方案：从最佳匹配的列名中提取日期
        if (this.basketData && this.basketData.length > 0) {
            console.log('使用备用方案查找日期');
            const columns = Object.keys(this.basketData[0]);
            const dateColumns = this.findDateColumns(columns);
            if (dateColumns.length > 0) {
                const dateInfo = dateColumns[0];
                console.log('备用方案找到的日期信息:', dateInfo);

                if (dateInfo.isCurrentPeriod && dateInfo.displayName) {
                    console.log('使用displayName:', dateInfo.displayName);
                    return dateInfo.displayName;
                }
                const result = `${dateInfo.month || 6}月${dateInfo.day}日`;
                console.log('生成的日期字符串:', result);
                return result;
            }
        }
        console.log('无法获取日期信息，返回"未知"');
        return '未知';
    }

    // 新增：格式化日期为完整的年月日格式
    // 严格按照README.md中的日期匹配策略显示实际匹配的日期
    getFormattedDateForLabel() {
        console.log('=== 获取标题显示日期 ===');
        console.log('bestDateInfo:', this.bestDateInfo);

        // 使用保存的最佳日期信息（与实际匹配逻辑完全一致）
        if (this.bestDateInfo && this.bestDateInfo.dateInfo) {
            const dateInfo = this.bestDateInfo.dateInfo;
            console.log('dateInfo:', dateInfo);

            const year = dateInfo.year || 2025;
            const month = dateInfo.month || 6;
            const day = dateInfo.day;

            console.log(`🎯 使用最佳日期信息: ${year}年${month}月${day}日`);
            console.log(`日期优先级: ${dateInfo.priority}`);
            console.log(`是否为本期: ${dateInfo.isCurrentPeriod}`);
            console.log(`列名: ${dateInfo.column}`);
            console.log(`显示名称: ${dateInfo.displayName}`);

            if (day) {
                const formattedDate = `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;
                console.log(`✅ 返回格式化日期: ${formattedDate}`);
                return formattedDate;
            }
        }

        // 备用方案：重新执行日期匹配逻辑（确保与实际匹配完全一致）
        if (this.basketData && this.basketData.length > 0) {
            console.log('使用备用方案重新查找最佳日期');
            const columns = Object.keys(this.basketData[0]);
            const dateColumns = this.findDateColumns(columns);

            if (dateColumns.length > 0) {
                // 按优先级排序，选择第一个（与findBestDateColumn逻辑一致）
                dateColumns.sort((a, b) => a.priority - b.priority);
                const bestDateInfo = dateColumns[0];

                const year = bestDateInfo.year || 2025;
                const month = bestDateInfo.month || 6;
                const day = bestDateInfo.day;

                console.log(`备用方案选择日期: ${year}年${month}月${day}日`);
                console.log(`日期优先级: ${bestDateInfo.priority}`);

                if (day) {
                    return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;
                }
            }
        }

        console.log('无法获取日期信息，使用默认标题');
        return '选中日期价格';
    }

    displayResults() {
        if (!this.comparisonResults || this.comparisonResults.length === 0) {
            this.showError('没有找到可比较的数据');
            return;
        }

        // 更新统计卡片
        this.updateStatistics();

        // 添加结果筛选功能
        this.addResultFilters();

        // 更新结果表格
        this.updateResultsTable();

        // 显示结果区域
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
    }

    clearData() {
        // 重置所有数据
        this.salesData = null;
        this.basketData = null;
        this.comparisonResults = [];
        
        // 重置UI
        document.getElementById('salesFile').value = '';
        document.getElementById('basketFile').value = '';
        document.getElementById('salesFileInfo').classList.remove('show');
        document.getElementById('basketFileInfo').classList.remove('show');
        document.getElementById('salesFileBox').classList.remove('file-selected');
        document.getElementById('basketFileBox').classList.remove('file-selected');
        document.getElementById('compareBtn').disabled = true;
        document.getElementById('resultsSection').style.display = 'none';
        document.getElementById('errorSection').style.display = 'none';
        document.getElementById('progressSection').style.display = 'none';
    }

    updateStatistics() {
        if (!this.statistics) return;

        document.getElementById('totalMatched').textContent = this.statistics.totalMatched;
        document.getElementById('avgPriceDiff').textContent = this.statistics.avgPriceDiff + '%';
        document.getElementById('matchedDate').textContent = this.statistics.matchedDate;

        // 更新标签文本为具体的匹配日期（完整年月日格式）
        const matchedDateLabel = document.getElementById('matchedDateLabel');
        const formattedDate = this.getFormattedDateForLabel();
        matchedDateLabel.textContent = formattedDate;
    }

    updateResultsTable() {
        const tbody = document.getElementById('resultsTableBody');
        tbody.innerHTML = '';

        for (const result of this.comparisonResults) {
            const row = document.createElement('tr');

            // 根据匹配状态设置行样式
            if (result.status === 'not_found') {
                row.classList.add('no-match');
            } else if (result.matchType === 'fuzzy') {
                row.classList.add('fuzzy-match');
            }

            row.innerHTML = `
                <td>${result.productName}</td>
                <td>${result.unit}</td>
                <td>${result.salesPrice !== null ? '¥' + result.salesPrice.toFixed(2) : '-'}</td>
                <td>${result.basketPrice !== null ? '¥' + result.basketPrice.toFixed(2) : '-'}</td>
                <td class="${this.getPriceDiffClass(result.priceDiff)}">
                    ${result.priceDiff !== null ?
                        (result.priceDiff >= 0 ? '+' : '') + result.priceDiff.toFixed(2) : '-'}
                </td>
                <td class="${this.getPriceDiffClass(result.priceDiff)}">
                    ${result.priceDiffPercent !== null ?
                        (result.priceDiffPercent >= 0 ? '+' : '') + result.priceDiffPercent.toFixed(1) + '%' : '-'}
                </td>
                <td>
                    <span class="status-badge ${result.status}">
                        ${this.getStatusText(result.status, result.matchType)}
                    </span>
                </td>
            `;

            tbody.appendChild(row);
        }
    }

    getPriceDiffClass(priceDiff) {
        if (priceDiff === null) return '';
        if (priceDiff > 0) return 'price-higher';
        if (priceDiff < 0) return 'price-lower';
        return 'price-equal';
    }

    getStatusText(status, matchType) {
        if (status === 'not_found') return '未匹配';
        
        switch (matchType) {
            case 'exact': return '精确匹配';
            case 'contains': return '包含匹配';
            case 'category': return '类别匹配';
            case 'fuzzy': return '模糊匹配';
            default: return '已匹配';
        }
    }

    exportResults() {
        if (!this.comparisonResults || this.comparisonResults.length === 0) {
            this.showError('没有可导出的数据');
            return;
        }

        try {
            // 准备导出数据
            const exportData = this.prepareExportData();

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 添加比价结果工作表
            const ws = XLSX.utils.json_to_sheet(exportData.results);
            XLSX.utils.book_append_sheet(wb, ws, '比价结果');

            // 添加统计摘要工作表
            const summaryWs = XLSX.utils.json_to_sheet(exportData.summary);
            XLSX.utils.book_append_sheet(wb, summaryWs, '统计摘要');

            // 生成文件名
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const filename = `菜篮子比价分析结果_${timestamp}.xlsx`;

            // 下载文件
            XLSX.writeFile(wb, filename);

            // 显示成功消息
            this.showSuccessMessage('结果已成功导出到: ' + filename);

        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    prepareExportData() {
        const results = this.comparisonResults.map(result => ({
            '商品名称': result.productName,
            '计价单位': result.unit,
            '销售价格': result.salesPrice !== null ? result.salesPrice : '',
            '菜篮子价格': result.basketPrice !== null ? result.basketPrice : '',
            '价格差异': result.priceDiff !== null ? result.priceDiff : '',
            '差异百分比': result.priceDiffPercent !== null ? result.priceDiffPercent + '%' : '',
            '匹配状态': this.getStatusText(result.status, result.matchType),
            '匹配类型': result.matchType || ''
        }));

        const summary = [
            { '统计项目': '总商品数', '数值': this.statistics.totalProducts },
            { '统计项目': '成功匹配数', '数值': this.statistics.totalMatched },
            { '统计项目': '匹配率', '数值': ((this.statistics.totalMatched / this.statistics.totalProducts) * 100).toFixed(2) + '%' },
            { '统计项目': '平均价差', '数值': this.statistics.avgPriceDiff + '%' },
            { '统计项目': '匹配日期', '数值': this.statistics.matchedDate },
            { '统计项目': '分析时间', '数值': new Date().toLocaleString('zh-CN') }
        ];

        return { results, summary };
    }

    showSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <div class="success-icon">✅</div>
            <div class="success-text">${message}</div>
        `;
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 400px;
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }

    // 工具方法
    showLoading(message = '正在处理...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = overlay.querySelector('.loading-text');
        text.textContent = message;
        overlay.style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showProgress() {
        document.getElementById('progressSection').style.display = 'block';
        this.updateProgress(0, '开始处理...');
    }

    hideProgress() {
        document.getElementById('progressSection').style.display = 'none';
    }

    updateProgress(percentage, message) {
        document.getElementById('progressFill').style.width = percentage + '%';
        document.getElementById('progressText').textContent = message;
    }

    showError(message, details = null) {
        console.error('Error:', message, details);

        document.getElementById('errorMessage').textContent = message;
        document.getElementById('errorSection').style.display = 'block';
        document.getElementById('errorSection').scrollIntoView({ behavior: 'smooth' });

        // 添加错误详情（如果有）
        if (details) {
            const errorContainer = document.querySelector('.error-content');
            let detailsElement = errorContainer.querySelector('.error-details');

            if (!detailsElement) {
                detailsElement = document.createElement('div');
                detailsElement.className = 'error-details';
                errorContainer.insertBefore(detailsElement, errorContainer.querySelector('.retry-btn'));
            }

            detailsElement.innerHTML = `<details><summary>查看详细信息</summary><pre>${details}</pre></details>`;
        }

        // 自动隐藏加载状态
        this.hideLoading();
        this.hideProgress();
    }

    hideError() {
        document.getElementById('errorSection').style.display = 'none';

        // 清除错误详情
        const errorDetails = document.querySelector('.error-details');
        if (errorDetails) {
            errorDetails.remove();
        }
    }

    // 添加不同类型的提示消息
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;

        const icon = this.getToastIcon(type);
        toast.innerHTML = `
            <div class="toast-icon">${icon}</div>
            <div class="toast-message">${message}</div>
            <button class="toast-close">×</button>
        `;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${this.getToastColor(type)};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 关闭按钮事件
        toast.querySelector('.toast-close').addEventListener('click', () => {
            this.removeToast(toast);
        });

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.removeToast(toast);
            }, duration);
        }

        return toast;
    }

    getToastIcon(type) {
        const icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        };
        return icons[type] || icons['info'];
    }

    getToastColor(type) {
        const colors = {
            'info': '#3498db',
            'success': '#27ae60',
            'warning': '#f39c12',
            'error': '#e74c3c'
        };
        return colors[type] || colors['info'];
    }

    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    // 添加数据验证方法
    validateSalesData(data) {
        const errors = [];

        if (!data || data.length === 0) {
            errors.push('销售价目表数据为空');
            return errors;
        }

        const firstRow = data[0];
        const requiredFields = ['物料名称', '计价单位', '市场价'];
        const availableFields = Object.keys(firstRow);

        for (const field of requiredFields) {
            const found = availableFields.some(f => f && f.includes(field.split('名称')[0]));
            if (!found) {
                errors.push(`缺少必需字段: ${field}`);
            }
        }

        // 检查数据质量
        let validRows = 0;
        for (const row of data) {
            const hasProductName = Object.values(row).some(val =>
                val && typeof val === 'string' && val.trim().length > 0
            );
            if (hasProductName) validRows++;
        }

        if (validRows === 0) {
            errors.push('没有找到有效的商品数据');
        } else if (validRows < data.length * 0.5) {
            errors.push(`数据质量较低，只有 ${validRows}/${data.length} 行包含有效数据`);
        }

        return errors;
    }

    validateBasketData(data) {
        const errors = [];

        if (!data || data.length === 0) {
            errors.push('菜篮子价格表数据为空');
            return errors;
        }

        const firstRow = data[0];
        const availableFields = Object.keys(firstRow);

        // 检查必需字段
        const hasProductField = availableFields.some(f => f && f.includes('商品'));
        const hasUnitField = availableFields.some(f => f && f.includes('单位'));
        const hasPriceField = availableFields.some(f => f && f.includes('本期'));

        if (!hasProductField) {
            errors.push('缺少商品名称字段');
        }
        if (!hasUnitField) {
            errors.push('缺少计价单位字段');
        }
        if (!hasPriceField) {
            errors.push('缺少价格数据字段（本期）');
        }

        return errors;
    }

    // 改进的文件解析方法
    async parseExcelFileWithValidation(file, fileType) {
        try {
            const data = await this.parseExcelFile(file);

            // 根据文件类型进行验证
            let errors = [];
            if (fileType === 'sales') {
                errors = this.validateSalesData(data);
            } else if (fileType === 'basket') {
                errors = this.validateBasketData(data);
            }

            if (errors.length > 0) {
                throw new Error(errors.join('\n'));
            }

            return data;
        } catch (error) {
            throw error;
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 添加键盘快捷键支持
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+O 打开文件
            if (e.ctrlKey && e.key === 'o') {
                e.preventDefault();
                if (!this.salesData) {
                    document.getElementById('salesFile').click();
                } else if (!this.basketData) {
                    document.getElementById('basketFile').click();
                }
            }

            // Ctrl+Enter 开始比价
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                if (!document.getElementById('compareBtn').disabled) {
                    this.startComparison();
                }
            }

            // Ctrl+S 导出结果
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                if (this.comparisonResults && this.comparisonResults.length > 0) {
                    this.exportResults();
                }
            }

            // Escape 清空数据
            if (e.key === 'Escape') {
                this.clearData();
            }
        });
    }

    // 添加文件验证
    validateFile(file) {
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (file.size > maxSize) {
            throw new Error('文件大小不能超过10MB');
        }

        // 简化类型检查，只检查文件扩展名
        const fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
            throw new Error('只支持Excel文件格式（.xlsx, .xls）');
        }

        return true;
    }

    // 添加数据预览功能
    showDataPreview(data, title) {
        if (!data || data.length === 0) return;

        const previewModal = document.createElement('div');
        previewModal.className = 'preview-modal';
        previewModal.innerHTML = `
            <div class="preview-content">
                <div class="preview-header">
                    <h3>${title} - 数据预览</h3>
                    <button class="close-preview">×</button>
                </div>
                <div class="preview-body">
                    <p>共 ${data.length} 行数据，显示前5行：</p>
                    <div class="preview-table">
                        ${this.generatePreviewTable(data.slice(0, 5))}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(previewModal);

        // 关闭预览
        previewModal.querySelector('.close-preview').addEventListener('click', () => {
            document.body.removeChild(previewModal);
        });

        previewModal.addEventListener('click', (e) => {
            if (e.target === previewModal) {
                document.body.removeChild(previewModal);
            }
        });
    }

    generatePreviewTable(data) {
        if (!data || data.length === 0) return '<p>无数据</p>';

        const headers = Object.keys(data[0]);
        let html = '<table class="preview-table-content"><thead><tr>';

        headers.forEach(header => {
            html += `<th>${header}</th>`;
        });
        html += '</tr></thead><tbody>';

        data.forEach(row => {
            html += '<tr>';
            headers.forEach(header => {
                html += `<td>${row[header] || ''}</td>`;
            });
            html += '</tr>';
        });

        html += '</tbody></table>';
        return html;
    }

    // 添加进度动画
    animateProgress() {
        const progressFill = document.getElementById('progressFill');
        progressFill.style.transition = 'width 0.5s ease-in-out';
    }

    // 添加结果筛选功能
    addResultFilters() {
        const filtersHtml = `
            <div class="result-filters">
                <label>
                    <input type="checkbox" id="filterMatched" checked> 显示已匹配
                </label>
                <label>
                    <input type="checkbox" id="filterUnmatched" checked> 显示未匹配
                </label>
                <label>
                    <input type="checkbox" id="filterFuzzy" checked> 显示模糊匹配
                </label>
                <select id="sortBy">
                    <option value="name">按商品名称排序</option>
                    <option value="basketPrice">按菜篮子价格排序</option>
                    <option value="salesPrice">按销售价格排序</option>
                    <option value="priceDiff">按价格差异排序</option>
                    <option value="priceDiffPercent">按差异百分比排序</option>
                </select>
            </div>
        `;

        const resultsHeader = document.querySelector('.results-header');
        if (resultsHeader && !document.querySelector('.result-filters')) {
            resultsHeader.insertAdjacentHTML('afterend', filtersHtml);
            this.bindFilterEvents();
        }
    }

    bindFilterEvents() {
        const filterMatched = document.getElementById('filterMatched');
        const filterUnmatched = document.getElementById('filterUnmatched');
        const filterFuzzy = document.getElementById('filterFuzzy');
        const sortBy = document.getElementById('sortBy');

        [filterMatched, filterUnmatched, filterFuzzy, sortBy].forEach(element => {
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
    }

    applyFilters() {
        if (!this.comparisonResults) return;

        let filteredResults = [...this.comparisonResults];

        // 应用筛选
        const showMatched = document.getElementById('filterMatched')?.checked;
        const showUnmatched = document.getElementById('filterUnmatched')?.checked;
        const showFuzzy = document.getElementById('filterFuzzy')?.checked;

        filteredResults = filteredResults.filter(result => {
            if (result.status === 'not_found' && !showUnmatched) return false;
            if (result.status === 'matched' && result.matchType === 'exact' && !showMatched) return false;
            if (result.status === 'matched' && result.matchType === 'fuzzy' && !showFuzzy) return false;
            return true;
        });

        // 应用排序
        const sortBy = document.getElementById('sortBy')?.value;
        console.log(`🔄 应用排序: ${sortBy}`);
        console.log(`排序前数据样本 (前3个):`, filteredResults.slice(0, 3).map(r => ({
            name: r.productName,
            basketPrice: r.basketPrice,
            salesPrice: r.salesPrice,
            priceDiff: r.priceDiff,
            priceDiffPercent: r.priceDiffPercent
        })));

        if (sortBy) {
            filteredResults.sort((a, b) => {
                switch (sortBy) {
                    case 'name':
                        return a.productName.localeCompare(b.productName);
                    case 'basketPrice':
                        // 按菜篮子价格排序（从高到低），null值排在最后
                        const basketPriceA = a.basketPrice;
                        const basketPriceB = b.basketPrice;

                        if (basketPriceA === null && basketPriceB === null) return 0;
                        if (basketPriceA === null) return 1; // null值排在后面
                        if (basketPriceB === null) return -1; // null值排在后面

                        return basketPriceB - basketPriceA; // 从高到低
                    case 'salesPrice':
                        // 按销售价格排序（从高到低），null值排在最后
                        const salesPriceA = a.salesPrice;
                        const salesPriceB = b.salesPrice;

                        if (salesPriceA === null && salesPriceB === null) return 0;
                        if (salesPriceA === null) return 1; // null值排在后面
                        if (salesPriceB === null) return -1; // null值排在后面

                        return salesPriceB - salesPriceA; // 从高到低
                    case 'priceDiff':
                        // 按价格差异排序（从大到小），null值排在最后
                        const priceDiffA = a.priceDiff;
                        const priceDiffB = b.priceDiff;

                        if (priceDiffA === null && priceDiffB === null) return 0;
                        if (priceDiffA === null) return 1;
                        if (priceDiffB === null) return -1;

                        return priceDiffB - priceDiffA;
                    case 'priceDiffPercent':
                        // 按差异百分比排序（从大到小），null值排在最后
                        const priceDiffPercentA = a.priceDiffPercent;
                        const priceDiffPercentB = b.priceDiffPercent;

                        if (priceDiffPercentA === null && priceDiffPercentB === null) return 0;
                        if (priceDiffPercentA === null) return 1;
                        if (priceDiffPercentB === null) return -1;

                        return priceDiffPercentB - priceDiffPercentA;
                    default:
                        return 0;
                }
            });

            console.log(`排序后数据样本 (前3个):`, filteredResults.slice(0, 3).map(r => ({
                name: r.productName,
                basketPrice: r.basketPrice,
                salesPrice: r.salesPrice,
                priceDiff: r.priceDiff,
                priceDiffPercent: r.priceDiffPercent
            })));
        }

        // 更新表格显示
        this.updateResultsTableWithData(filteredResults);
    }

    updateResultsTableWithData(results) {
        const tbody = document.getElementById('resultsTableBody');
        tbody.innerHTML = '';

        for (const result of results) {
            const row = document.createElement('tr');

            if (result.status === 'not_found') {
                row.classList.add('no-match');
            } else if (result.matchType === 'fuzzy') {
                row.classList.add('fuzzy-match');
            }

            row.innerHTML = `
                <td>${result.productName}</td>
                <td>${result.unit}</td>
                <td>${result.salesPrice !== null ? '¥' + result.salesPrice.toFixed(2) : '-'}</td>
                <td>${result.basketPrice !== null ? '¥' + result.basketPrice.toFixed(2) : '-'}</td>
                <td class="${this.getPriceDiffClass(result.priceDiff)}">
                    ${result.priceDiff !== null ?
                        (result.priceDiff >= 0 ? '+' : '') + result.priceDiff.toFixed(2) : '-'}
                </td>
                <td class="${this.getPriceDiffClass(result.priceDiff)}">
                    ${result.priceDiffPercent !== null ?
                        (result.priceDiffPercent >= 0 ? '+' : '') + result.priceDiffPercent.toFixed(1) + '%' : '-'}
                </td>
                <td>
                    <span class="status-badge ${result.status}">
                        ${this.getStatusText(result.status, result.matchType)}
                    </span>
                </td>
            `;

            tbody.appendChild(row);
        }
    }

    // 显示帮助模态框
    showHelp() {
        const helpModal = document.getElementById('helpModal');
        helpModal.style.display = 'flex';

        // 绑定关闭事件
        const closeBtn = helpModal.querySelector('.close-help');
        closeBtn.addEventListener('click', () => this.hideHelp());

        helpModal.addEventListener('click', (e) => {
            if (e.target === helpModal) {
                this.hideHelp();
            }
        });
    }

    hideHelp() {
        document.getElementById('helpModal').style.display = 'none';
    }

    // 加载示例文件
    async loadDemoFiles() {
        try {
            this.showLoading('正在加载示例文件...');

            // 检查示例文件是否存在
            const salesFileName = '2025年7月销售价目表2025.xlsx';
            const basketFileName = '广东省菜篮子价格监测日报表（2025年06月01日_2025年06月26日）监测对比表.xlsx';

            // 创建文件输入元素来模拟文件选择
            const salesInput = document.getElementById('salesFile');
            const basketInput = document.getElementById('basketFile');

            // 尝试通过fetch加载文件（仅在本地环境有效）
            try {
                const salesResponse = await fetch(salesFileName);
                const basketResponse = await fetch(basketFileName);

                if (salesResponse.ok && basketResponse.ok) {
                    const salesBlob = await salesResponse.blob();
                    const basketBlob = await basketResponse.blob();

                    // 创建File对象
                    const salesFile = new File([salesBlob], salesFileName, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                    const basketFile = new File([basketBlob], basketFileName, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

                    // 模拟文件上传
                    await this.loadDemoFile(salesFile, 'sales');
                    await this.loadDemoFile(basketFile, 'basket');

                    this.showToast('示例文件加载成功！可以开始比价分析了。', 'success');
                } else {
                    throw new Error('示例文件不存在');
                }
            } catch (fetchError) {
                // 如果fetch失败，提示用户手动上传
                this.showToast('请手动上传Excel文件进行测试', 'info', 5000);
            }

        } catch (error) {
            this.showError('加载示例文件失败', error.message);
        } finally {
            this.hideLoading();
        }
    }

    async loadDemoFile(file, type) {
        try {
            this.validateFile(file);

            if (type === 'sales') {
                this.salesData = await this.parseExcelFileWithValidation(file, 'sales');
                this.updateFileInfo('salesFileInfo', file.name, this.salesData.length);
                this.updateUploadBox('salesFileBox', true);
            } else if (type === 'basket') {
                this.basketData = await this.parseExcelFileWithValidation(file, 'basket');
                this.updateFileInfo('basketFileInfo', file.name, this.basketData.length);
                this.updateUploadBox('basketFileBox', true);
            }

            this.checkReadyToCompare();
        } catch (error) {
            throw error;
        }
    }

    // 添加性能监控
    startPerformanceMonitoring() {
        this.performanceStart = performance.now();
    }

    endPerformanceMonitoring(operation) {
        if (this.performanceStart) {
            const duration = performance.now() - this.performanceStart;
            console.log(`${operation} 耗时: ${duration.toFixed(2)}ms`);
            this.performanceStart = null;
        }
    }

    // 添加数据统计
    logDataStatistics() {
        if (this.salesData && this.basketData && this.comparisonResults) {
            const stats = {
                salesDataCount: this.salesData.length,
                basketDataCount: this.basketData.length,
                totalMatched: this.statistics.totalMatched,
                matchRate: ((this.statistics.totalMatched / this.salesData.length) * 100).toFixed(2) + '%',
                avgPriceDiff: this.statistics.avgPriceDiff + '%'
            };

            console.log('数据统计:', stats);
            return stats;
        }
        return null;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing PriceComparisonTool...');
    try {
        const tool = new PriceComparisonTool();

        // 将实例设置为全局变量，方便测试
        window.priceComparisonTool = tool;

        console.log('PriceComparisonTool initialized successfully');

        // 添加全局错误处理
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });

        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
        });

    } catch (error) {
        console.error('Failed to initialize PriceComparisonTool:', error);
    }
});
