<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据结构调试工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        .file-input {
            margin: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .data-preview {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 4px;
            max-height: 300px;
            overflow: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>菜篮子比价工具 - 数据结构调试</h1>
        
        <div class="upload-section">
            <h3>上传Excel文件</h3>
            <div>
                <label>销售价目表：</label>
                <input type="file" id="salesFile" class="file-input" accept=".xlsx,.xls">
            </div>
            <div>
                <label>菜篮子价格表：</label>
                <input type="file" id="basketFile" class="file-input" accept=".xlsx,.xls">
            </div>
            <button class="btn" onclick="analyzeData()">分析数据结构</button>
            <button class="btn" onclick="testMatching()">测试商品匹配</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div id="logSection" class="log-section"></div>
        
        <div id="dataPreview" class="data-preview" style="display: none;">
            <h3>数据预览</h3>
            <div id="previewContent"></div>
        </div>
    </div>

    <script>
        let salesData = null;
        let basketData = null;

        function log(message) {
            const logSection = document.getElementById('logSection');
            const timestamp = new Date().toLocaleTimeString();
            logSection.textContent += `[${timestamp}] ${message}\n`;
            logSection.scrollTop = logSection.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logSection').textContent = '';
        }

        // 文件上传处理
        document.getElementById('salesFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                log(`正在读取销售价目表: ${file.name}`);
                readExcelFile(file, (data) => {
                    salesData = data;
                    log(`销售价目表读取完成，共 ${data.length} 行数据`);
                    showDataPreview('销售价目表', data);
                });
            }
        });

        document.getElementById('basketFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                log(`正在读取菜篮子价格表: ${file.name}`);
                readExcelFile(file, (data) => {
                    basketData = data;
                    log(`菜篮子价格表读取完成，共 ${data.length} 行数据`);
                    showDataPreview('菜篮子价格表', data);
                });
            }
        });

        function readExcelFile(file, callback) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, {
                        type: 'array',
                        cellDates: true,
                        cellNF: false,
                        cellText: false,
                        raw: false,
                        defval: ''
                    });

                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // 使用数组格式解析
                    const rawData = XLSX.utils.sheet_to_json(worksheet, {
                        header: 1,
                        defval: '',
                        blankrows: false,
                        raw: false
                    });

                    log(`原始数据行数: ${rawData.length}`);
                    log(`原始数据前3行:`, rawData.slice(0, 3));

                    // 检查是否是菜篮子价格表
                    const isBasketPriceTable = rawData.some(row =>
                        row && row.some(cell =>
                            cell && typeof cell === 'string' &&
                            (cell.includes('菜篮子价格监测') ||
                             cell.includes('监测日报表') ||
                             cell.includes('监测对比表'))
                        )
                    );

                    log(`是否为菜篮子价格表: ${isBasketPriceTable}`);

                    let processedData;
                    if (isBasketPriceTable) {
                        processedData = parseBasketPriceTable(rawData);
                    } else {
                        processedData = parseRegularTable(rawData);
                    }

                    callback(processedData);
                } catch (error) {
                    log(`读取文件失败: ${error.message}`);
                }
            };
            reader.readAsArrayBuffer(file);
        }

        function showDataPreview(title, data) {
            const previewSection = document.getElementById('dataPreview');
            const previewContent = document.getElementById('previewContent');
            
            if (data && data.length > 0) {
                const columns = Object.keys(data[0]);
                let html = `<h4>${title} (前5行)</h4>`;
                html += '<table><thead><tr>';
                columns.forEach(col => {
                    html += `<th>${col}</th>`;
                });
                html += '</tr></thead><tbody>';
                
                data.slice(0, 5).forEach(row => {
                    html += '<tr>';
                    columns.forEach(col => {
                        html += `<td>${row[col] || ''}</td>`;
                    });
                    html += '</tr>';
                });
                html += '</tbody></table>';
                
                previewContent.innerHTML = html;
                previewSection.style.display = 'block';
            }
        }

        function analyzeData() {
            if (!salesData || !basketData) {
                log('❌ 请先上传两个Excel文件');
                return;
            }

            log('🔍 开始分析数据结构...');
            
            // 分析销售数据
            log('\n=== 销售价目表分析 ===');
            const salesColumns = Object.keys(salesData[0]);
            log(`列名: ${salesColumns.join(', ')}`);
            log(`数据行数: ${salesData.length}`);
            log('前3行数据:');
            salesData.slice(0, 3).forEach((row, index) => {
                log(`  行${index + 1}: ${JSON.stringify(row)}`);
            });

            // 分析菜篮子数据
            log('\n=== 菜篮子价格表分析 ===');
            const basketColumns = Object.keys(basketData[0]);
            log(`列名: ${basketColumns.join(', ')}`);
            log(`数据行数: ${basketData.length}`);
            log('前3行数据:');
            basketData.slice(0, 3).forEach((row, index) => {
                log(`  行${index + 1}: ${JSON.stringify(row)}`);
            });

            // 查找关键列
            log('\n=== 关键列识别 ===');
            
            // 销售数据关键列
            const salesProductCol = findColumn(salesColumns, ['物料名称', '商品名称', '产品名称', '名称']);
            const salesUnitCol = findColumn(salesColumns, ['计价单位', '单位', '规格']);
            const salesPriceCol = findColumn(salesColumns, ['市场价', '价格', '单价', '售价']);
            
            log(`销售数据 - 商品名称列: ${salesProductCol}`);
            log(`销售数据 - 单位列: ${salesUnitCol}`);
            log(`销售数据 - 价格列: ${salesPriceCol}`);

            // 菜篮子数据关键列
            const basketProductCol = findColumn(basketColumns, ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']) || basketColumns[0];
            const basketUnitCol = findColumn(basketColumns, ['计价单位', '单位', '规格', '计量单位']);
            
            log(`菜篮子数据 - 商品名称列: ${basketProductCol}`);
            log(`菜篮子数据 - 单位列: ${basketUnitCol}`);

            // 查找日期列
            log('\n=== 日期列分析 ===');
            const dateColumns = findDateColumns(basketColumns);
            log(`找到 ${dateColumns.length} 个可能的价格列:`);
            dateColumns.forEach(col => {
                log(`  ${col.column} (索引: ${col.columnIndex}, 日期: ${col.day})`);
                // 显示该列的前几个值
                log(`    前3个值: ${basketData.slice(0, 3).map(row => `"${row[col.column]}"`).join(', ')}`);
            });
        }

        function findColumn(columns, possibleNames) {
            for (const name of possibleNames) {
                const found = columns.find(col => col && col.includes(name));
                if (found) return found;
            }
            return null;
        }

        function findDateColumns(columns) {
            const dateColumns = [];
            const datePattern = /(\d{1,2})[日号]/;
            const monthPattern = /(\d{1,2})月(\d{1,2})[日号]/;
            const fullDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;
            // 新增：组合格式的日期列匹配，如 "2025年06月26日-本期"
            const combinedDatePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日-(.+)/;

            for (let i = 0; i < columns.length; i++) {
                const col = columns[i];
                if (!col) continue;

                // 优先查找组合格式的日期列（如 "2025年06月26日-本期"）
                if (combinedDatePattern.test(col)) {
                    const match = col.match(combinedDatePattern);
                    if (match) {
                        const day = parseInt(match[3]);
                        const priceType = match[4];

                        // 优先选择"本期"价格
                        const priority = priceType.includes('本期') ?
                            getDatePriority(day) - 100 : // 本期优先级更高
                            getDatePriority(day);

                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: day,
                            year: parseInt(match[1]),
                            month: parseInt(match[2]),
                            priceType: priceType,
                            priority: priority
                        });
                    }
                }
                // 查找包含"本期"的日期列
                else if (datePattern.test(col) && col.includes('本期')) {
                    const match = col.match(datePattern);
                    if (match) {
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: parseInt(match[1]),
                            priority: getDatePriority(parseInt(match[1])) - 50 // 本期优先级高
                        });
                    }
                }
                // 查找完整日期格式
                else if (fullDatePattern.test(col)) {
                    const match = col.match(fullDatePattern);
                    if (match) {
                        const day = parseInt(match[3]);
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: day,
                            year: parseInt(match[1]),
                            month: parseInt(match[2]),
                            priority: getDatePriority(day)
                        });
                    }
                }
                // 查找月日格式
                else if (monthPattern.test(col)) {
                    const match = col.match(monthPattern);
                    if (match) {
                        dateColumns.push({
                            column: col,
                            columnIndex: i,
                            day: parseInt(match[2]),
                            month: parseInt(match[1]),
                            priority: getDatePriority(parseInt(match[2]))
                        });
                    }
                }
                // 查找其他价格列
                else if (col.includes('价格') || col.includes('单价') || col.includes('元') || /\d+[日号]/.test(col)) {
                    const match = col.match(datePattern);
                    const day = match ? parseInt(match[1]) : 25;
                    dateColumns.push({
                        column: col,
                        columnIndex: i,
                        day: day,
                        priority: getDatePriority(day)
                    });
                }
            }

            return dateColumns.sort((a, b) => a.priority - b.priority);
        }

        function getDatePriority(day) {
            // 严格按照README.md中的日期匹配策略：25日→26日→27日→28日→29日...
            const priorityOrder = [25, 26, 27, 28, 29];

            const index = priorityOrder.indexOf(day);
            if (index !== -1) {
                return index; // 返回在优先级数组中的位置，越小优先级越高
            }

            // 如果日期不在预定义列表中，给予较低优先级
            // 按照距离25日的远近来排序，但优先级低于前5个日期
            const distance = Math.abs(day - 25);
            return 10 + distance; // 确保优先级低于前5个日期
        }

        function parseBasketPriceTable(rawData) {
            log('解析菜篮子价格表...');

            let basicHeaderRow = null;
            let dateHeaderRow = null;
            let priceTypeRow = null;
            let dataStartRow = 4;

            // 查找基本表头行
            for (let i = 0; i < Math.min(rawData.length, 5); i++) {
                const row = rawData[i];
                if (row && Array.isArray(row)) {
                    const hasBasicFields = row.some(cell =>
                        cell && typeof cell === 'string' &&
                        (cell.includes('商品/分类名称') || cell.includes('商品名称') ||
                         cell.includes('计价单位') || cell.includes('规格等级'))
                    );

                    if (hasBasicFields) {
                        basicHeaderRow = row;
                        log(`找到基本表头行 ${i}:`, row.slice(0, 10));

                        if (i + 1 < rawData.length) {
                            const nextRow = rawData[i + 1];
                            if (nextRow && nextRow.some(cell =>
                                cell && cell.toString().includes('2025年'))) {
                                dateHeaderRow = nextRow;
                                log(`找到日期表头行 ${i + 1}:`, nextRow.slice(0, 10));
                            }
                        }

                        if (i + 2 < rawData.length) {
                            const priceRow = rawData[i + 2];
                            if (priceRow && priceRow.some(cell =>
                                cell && (cell.toString().includes('本期') || cell.toString().includes('上期')))) {
                                priceTypeRow = priceRow;
                                dataStartRow = i + 3;
                                log(`找到价格类型行 ${i + 2}:`, priceRow.slice(0, 10));
                            }
                        }
                        break;
                    }
                }
            }

            if (!basicHeaderRow) {
                log('未找到基本表头行，使用默认处理');
                basicHeaderRow = rawData[0] || [];
            }

            // 构建合并的表头
            const maxColumns = Math.max(...rawData.map(row => row ? row.length : 0));
            const mergedHeaders = new Array(maxColumns).fill('');

            // 处理基本字段列
            if (basicHeaderRow) {
                for (let i = 0; i < Math.min(4, basicHeaderRow.length); i++) {
                    const cell = basicHeaderRow[i];
                    if (cell && cell.toString().trim() !== '') {
                        mergedHeaders[i] = cell.toString().trim();
                    }
                }
            }

            // 处理日期相关的列
            if (dateHeaderRow && priceTypeRow) {
                log('开始合并日期和价格类型列...');
                log(`日期行长度: ${dateHeaderRow.length}, 价格类型行长度: ${priceTypeRow.length}`);

                for (let i = 4; i < maxColumns; i++) {
                    const dateCell = dateHeaderRow[i];
                    const priceTypeCell = priceTypeRow[i];

                    log(`列${i}: 日期="${dateCell}", 价格类型="${priceTypeCell}"`);

                    let columnName = '';

                    if (dateCell && dateCell.toString().trim() !== '') {
                        const dateStr = dateCell.toString().trim();
                        if (priceTypeCell && priceTypeCell.toString().trim() !== '') {
                            const priceType = priceTypeCell.toString().trim();
                            columnName = `${dateStr}-${priceType}`;
                        } else {
                            columnName = dateStr;
                        }
                    } else if (priceTypeCell && priceTypeCell.toString().trim() !== '') {
                        columnName = priceTypeCell.toString().trim();
                    } else {
                        columnName = `列${i + 1}`;
                    }

                    log(`列${i}最终名称: "${columnName}"`);
                    mergedHeaders[i] = columnName;
                }
            }

            // 为空的列生成默认名称
            for (let i = 0; i < mergedHeaders.length; i++) {
                if (!mergedHeaders[i] || mergedHeaders[i] === '') {
                    mergedHeaders[i] = `列${i + 1}`;
                }
            }

            log('最终表头:', mergedHeaders);

            // 处理数据行
            const dataRows = rawData.slice(dataStartRow);
            const processedData = dataRows
                .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                .map(row => {
                    const obj = {};
                    mergedHeaders.forEach((header, index) => {
                        obj[header] = (row[index] !== null && row[index] !== undefined) ? row[index].toString() : '';
                    });
                    return obj;
                })
                .filter(obj => {
                    const values = Object.values(obj);
                    const hasContent = values.some(val => val && val.toString().trim().length > 0);
                    return hasContent;
                });

            log(`菜篮子价格表处理完成，数据行数: ${processedData.length}`);
            return processedData;
        }

        function parseRegularTable(rawData) {
            log('解析常规表格...');

            let headerRowIndex = -1;
            const keyFields = ['物料名称', '商品', '名称', '计价单位', '单位', '价格', '市场价'];

            for (let i = 0; i < Math.min(rawData.length, 20); i++) {
                const row = rawData[i];
                if (row && Array.isArray(row)) {
                    const nonEmptyCells = row.filter(cell => cell && cell.toString().trim() !== '').length;

                    if (nonEmptyCells >= 3) {
                        const hasKeyFields = row.some(cell =>
                            cell && typeof cell === 'string' &&
                            keyFields.some(field => cell.includes(field))
                        );

                        if (hasKeyFields) {
                            headerRowIndex = i;
                            log(`找到表头行 ${i}:`, row);
                            break;
                        }
                    }
                }
            }

            if (headerRowIndex === -1) {
                log('未找到表头行，使用第一行');
                headerRowIndex = 0;
            }

            const headers = rawData[headerRowIndex] || [];
            const dataRows = rawData.slice(headerRowIndex + 1);

            const processedData = dataRows
                .filter(row => row && row.some(cell => cell !== null && cell !== undefined && cell !== ''))
                .map(row => {
                    const obj = {};
                    headers.forEach((header, index) => {
                        if (header) {
                            obj[header] = row[index] || '';
                        }
                    });
                    return obj;
                })
                .filter(obj => {
                    const values = Object.values(obj);
                    const hasContent = values.some(val => val && String(val).trim().length > 0);
                    return hasContent;
                });

            log(`常规表格处理完成，数据行数: ${processedData.length}`);
            return processedData;
        }

        function testMatching() {
            if (!salesData || !basketData) {
                log('❌ 请先上传两个Excel文件并分析数据结构');
                return;
            }

            log('\n🧪 开始测试商品匹配...');

            // 获取数据结构
            const salesColumns = Object.keys(salesData[0]);
            const basketColumns = Object.keys(basketData[0]);

            const salesProductCol = findColumn(salesColumns, ['物料名称', '商品名称', '产品名称', '名称']);
            const basketProductCol = findColumn(basketColumns, ['商品/分类名称', '商品名称', '分类名称', '名称', '商品', '品名']) || basketColumns[0];
            const dateColumns = findDateColumns(basketColumns);

            log(`销售商品列: ${salesProductCol}`);
            log(`菜篮子商品列: ${basketProductCol}`);
            log(`找到的日期列数量: ${dateColumns.length}`);

            if (dateColumns.length > 0) {
                log('日期列详情:');
                dateColumns.forEach((col, index) => {
                    log(`  ${index + 1}. ${col.column} (优先级: ${col.priority})`);
                });
            }

            if (!salesProductCol || !basketProductCol || dateColumns.length === 0) {
                log('❌ 缺少必要的列信息');
                if (!salesProductCol) log('  - 未找到销售商品名称列');
                if (!basketProductCol) log('  - 未找到菜篮子商品名称列');
                if (dateColumns.length === 0) log('  - 未找到日期价格列');
                return;
            }

            const bestDateColumn = dateColumns[0].column;
            log(`使用价格列: ${bestDateColumn}`);

            // 显示菜篮子数据样本
            log('\n=== 菜篮子商品样本 ===');
            basketData.slice(0, 10).forEach((item, index) => {
                const productName = item[basketProductCol];
                const priceValue = item[bestDateColumn];
                log(`  ${index + 1}. "${productName}" - 价格: "${priceValue}"`);
            });

            // 测试前5个销售商品的匹配
            log('\n=== 匹配测试结果 ===');
            salesData.slice(0, 5).forEach((salesItem, index) => {
                const productName = salesItem[salesProductCol];
                log(`\n测试商品 ${index + 1}: "${productName}"`);

                // 精确匹配
                const exactMatch = basketData.find(item => {
                    const basketProduct = item[basketProductCol];
                    return basketProduct === productName;
                });

                if (exactMatch) {
                    const priceValue = exactMatch[bestDateColumn];
                    log(`  ✓ 精确匹配: "${exactMatch[basketProductCol]}" - 价格: "${priceValue}"`);
                } else {
                    log(`  ✗ 精确匹配失败`);

                    // 包含匹配
                    const containsMatch = basketData.find(item => {
                        const basketProduct = item[basketProductCol];
                        return basketProduct && (basketProduct.includes(productName) || productName.includes(basketProduct));
                    });

                    if (containsMatch) {
                        const priceValue = containsMatch[bestDateColumn];
                        log(`  ✓ 包含匹配: "${containsMatch[basketProductCol]}" - 价格: "${priceValue}"`);
                    } else {
                        log(`  ✗ 包含匹配失败`);

                        // 尝试清理后的匹配
                        const cleanProductName = productName.replace(/[0-9\-\(\)（）]/g, '').trim();
                        log(`  清理后的商品名: "${cleanProductName}"`);

                        const cleanMatch = basketData.find(item => {
                            const basketProduct = item[basketProductCol];
                            if (!basketProduct) return false;
                            const cleanBasketName = basketProduct.replace(/[0-9\-\(\)（）]/g, '').trim();
                            return cleanBasketName.includes(cleanProductName) || cleanProductName.includes(cleanBasketName);
                        });

                        if (cleanMatch) {
                            const priceValue = cleanMatch[bestDateColumn];
                            log(`  ✓ 清理匹配: "${cleanMatch[basketProductCol]}" - 价格: "${priceValue}"`);
                        } else {
                            log(`  ✗ 所有匹配方式都失败`);
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
